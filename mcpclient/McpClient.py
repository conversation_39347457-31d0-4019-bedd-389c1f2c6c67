# 你是一个支持 标准协议的 MCP Client 工具类

# 你要干的事情是在 agent启动时 根据标准的 MCP定义 从 tool.json文件 获取 MCP 标准配置， 然后 调用 tools/list → 得到一堆工具定义（名字、描述、参数 schema）
# 发现工具（Discovery）：调用 MCP 的 tools/list，拿到当前 server 暴露的能力（工具名、参数 schema、返回格式）。
# 把每个 MCP 工具转成 OpenAI function call 的 JSON Schema，丢给模型。

# 同时你还负责 MCP Server的调用， 获取返回信息， 返回给大模型。

import json
import subprocess
import asyncio
import aiohttp
import os
import sys
from typing import Dict, List, Any, Optional, Union
from datetime import datetime
import logging

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


class McpServerConfig:
    """MCP服务器配置类"""
    def __init__(self, server_id: str, config: Dict[str, Any]):
        self.server_id = server_id
        self.name = config.get('name', '')
        self.type = config.get('type', 'stdio')  # stdio 或 streamableHttp
        self.description = config.get('description', '')
        self.is_active = config.get('isActive', True)
        self.registry_url = config.get('registryUrl', '')
        
        # stdio类型配置
        self.command = config.get('command', '')
        self.args = config.get('args', [])
        
        # HTTP类型配置
        self.base_url = config.get('baseUrl', '')
        
    def __str__(self) -> str:
        return f"McpServer({self.name}, {self.type}, active={self.is_active})"


class McpTool:
    """MCP工具定义类"""
    def __init__(self, name: str, description: str, input_schema: Dict[str, Any]):
        self.name = name
        self.description = description
        self.input_schema = input_schema
        
    def to_openai_function(self) -> Dict[str, Any]:
        """转换为OpenAI function call格式"""
        return {
            "type": "function",
            "function": {
                "name": self.name,
                "description": self.description,
                "parameters": self.input_schema
            }
        }
    
    def __str__(self) -> str:
        return f"McpTool({self.name}: {self.description})"


class McpClient:
    """MCP Client 工具类 - 支持标准MCP协议"""
    
    def __init__(self, config_file: str = "mcpclient/tool.json"):
        """
        初始化MCP客户端
        
        Args:
            config_file: MCP服务器配置文件路径
        """
        self.config_file = config_file
        self.servers: Dict[str, McpServerConfig] = {}
        self.tools: Dict[str, McpTool] = {}  # 所有可用工具
        self.server_processes: Dict[str, subprocess.Popen] = {}  # stdio进程
        self.logger = logging.getLogger(__name__)
        
        # 加载配置
        self.load_config()
    
    def load_config(self) -> bool:
        """
        从tool.json文件加载MCP服务器配置
        
        Returns:
            是否加载成功
        """
        try:
            config_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), self.config_file)
            
            with open(config_path, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            
            mcp_servers = config_data.get('mcpServers', {})
            
            for server_id, server_config in mcp_servers.items():
                if server_config.get('isActive', True):
                    self.servers[server_id] = McpServerConfig(server_id, server_config)
                    self.logger.info(f"加载MCP服务器配置: {self.servers[server_id]}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"加载MCP配置失败: {str(e)}")
            return False
    
    async def discover_tools(self) -> bool:
        """
        发现所有MCP服务器的工具
        调用每个服务器的tools/list接口
        
        Returns:
            是否发现成功
        """
        success_count = 0
        
        for server_id, server_config in self.servers.items():
            try:
                if server_config.type == 'stdio':
                    tools = await self._discover_stdio_tools(server_config)
                elif server_config.type == 'streamableHttp':
                    tools = await self._discover_http_tools(server_config)
                else:
                    self.logger.warning(f"不支持的服务器类型: {server_config.type}")
                    continue
                
                # 添加工具到工具集合
                for tool in tools:
                    tool_key = f"{server_id}:{tool.name}"
                    self.tools[tool_key] = tool
                    self.logger.info(f"发现工具: {tool}")
                
                success_count += 1
                
            except Exception as e:
                self.logger.error(f"发现服务器 {server_id} 工具失败: {str(e)}")
        
        self.logger.info(f"工具发现完成，成功连接 {success_count}/{len(self.servers)} 个服务器，发现 {len(self.tools)} 个工具")
        return success_count > 0
    
    async def _discover_stdio_tools(self, server_config: McpServerConfig) -> List[McpTool]:
        """
        发现stdio类型服务器的工具
        
        Args:
            server_config: 服务器配置
            
        Returns:
            工具列表
        """
        tools = []
        process = None
        
        try:
            # 启动stdio进程
            process = subprocess.Popen(
                [server_config.command] + server_config.args,
                stdin=subprocess.PIPE,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                bufsize=0  # 无缓冲
            )
            
            # 保存进程到字典中，以便后续工具调用使用
            self.server_processes[server_config.server_id] = process
            
            # 等待进程启动
            await asyncio.sleep(2)
            
            # 检查进程是否还在运行
            if process.poll() is not None:
                self.logger.error(f"stdio进程启动失败，退出码: {process.returncode}")
                # 如果进程启动失败，从字典中移除
                self.server_processes.pop(server_config.server_id, None)
                return tools
            
            # 步骤1: 发送initialize请求
            init_request = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "initialize",
                "params": {
                    "protocolVersion": "2024-11-05",
                    "capabilities": {},
                    "clientInfo": {
                        "name": "mcp-client",
                        "version": "1.0.0"
                    }
                }
            }
            
            init_json = json.dumps(init_request) + "\n"
            process.stdin.write(init_json)
            process.stdin.flush()
            
            # 读取initialize响应
            try:
                async def read_response():
                    loop = asyncio.get_event_loop()
                    return await loop.run_in_executor(None, process.stdout.readline)
                
                init_response_line = await asyncio.wait_for(read_response(), timeout=10.0)
                
                if not init_response_line:
                    self.logger.error(f"未收到initialize响应，服务器: {server_config.name}")
                    return tools
                
                init_response = json.loads(init_response_line.strip())
                
                if 'error' in init_response:
                    self.logger.error(f"initialize失败: {init_response['error']}")
                    return tools
                
                self.logger.info(f"MCP服务器初始化成功: {server_config.name}")
                
            except asyncio.TimeoutError:
                self.logger.error(f"initialize超时，服务器: {server_config.name}")
                return tools
            except json.JSONDecodeError as e:
                self.logger.error(f"initialize响应JSON解析失败: {e}")
                return tools
            
            # 步骤2: 发送initialized通知
            initialized_notification = {
                "jsonrpc": "2.0",
                "method": "notifications/initialized",
                "params": {}
            }
            
            initialized_json = json.dumps(initialized_notification) + "\n"
            process.stdin.write(initialized_json)
            process.stdin.flush()
            
            # 等待一下让服务器处理
            await asyncio.sleep(0.5)
            
            # 步骤3: 发送tools/list请求
            tools_request = {
                "jsonrpc": "2.0",
                "id": 2,
                "method": "tools/list",
                "params": {}
            }
            
            tools_json = json.dumps(tools_request) + "\n"
            process.stdin.write(tools_json)
            process.stdin.flush()
            
            # 读取tools/list响应
            try:
                tools_response_line = await asyncio.wait_for(read_response(), timeout=10.0)
                
                if tools_response_line:
                    tools_response = json.loads(tools_response_line.strip())
                    
                    if 'result' in tools_response and 'tools' in tools_response['result']:
                        for tool_data in tools_response['result']['tools']:
                            tool = McpTool(
                                name=tool_data.get('name', ''),
                                description=tool_data.get('description', ''),
                                input_schema=tool_data.get('inputSchema', {})
                            )
                            tools.append(tool)
                        self.logger.info(f"发现 {len(tools)} 个工具，服务器: {server_config.name}")
                    elif 'error' in tools_response:
                        self.logger.error(f"tools/list返回错误: {tools_response['error']}")
                    else:
                        self.logger.warning(f"tools/list响应格式异常: {tools_response}")
                else:
                    self.logger.warning(f"未收到tools/list响应，服务器: {server_config.name}")
                    
            except asyncio.TimeoutError:
                self.logger.error(f"tools/list超时，服务器: {server_config.name}")
            except json.JSONDecodeError as e:
                self.logger.error(f"tools/list响应JSON解析失败: {e}")
            
        except Exception as e:
            self.logger.error(f"stdio工具发现失败: {str(e)}")
        finally:
            # 保持进程运行，不要终止它
            # MCP服务器需要保持运行状态以便后续调用工具
            pass
            
        return tools
    
    async def _discover_http_tools(self, server_config: McpServerConfig) -> List[McpTool]:
        """
        发现HTTP类型服务器的工具
        
        Args:
            server_config: 服务器配置
            
        Returns:
            工具列表
        """
        tools = []
        
        try:
            async with aiohttp.ClientSession() as session:
                # 构建tools/list请求
                request_data = {
                    "jsonrpc": "2.0",
                    "id": 1,
                    "method": "tools/list",
                    "params": {}
                }
                
                async with session.post(
                    server_config.base_url,
                    json=request_data,
                    headers={
                        'Content-Type': 'application/json',
                        'Accept': 'application/json, text/event-stream'
                    }
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        
                        if 'result' in result and 'tools' in result['result']:
                            for tool_data in result['result']['tools']:
                                tool = McpTool(
                                    name=tool_data.get('name', ''),
                                    description=tool_data.get('description', ''),
                                    input_schema=tool_data.get('inputSchema', {})
                                )
                                tools.append(tool)
                    else:
                        self.logger.error(f"HTTP请求失败: {response.status}")
                        
        except Exception as e:
            self.logger.error(f"HTTP工具发现失败: {str(e)}")
            
        return tools
    
    def get_openai_functions(self) -> List[Dict[str, Any]]:
        """
        获取所有工具的OpenAI function call格式
        
        Returns:
            OpenAI function call格式的工具列表
        """
        return [tool.to_openai_function() for tool in self.tools.values()]
    
    def get_tool_by_name(self, tool_name: str) -> Optional[McpTool]:
        """
        根据工具名称获取工具
        
        Args:
            tool_name: 工具名称
            
        Returns:
            工具对象或None
        """
        for tool_key, tool in self.tools.items():
            if tool.name == tool_name:
                return tool
        return None
    
    async def call_tool(self, tool_name: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """
        调用MCP工具
        
        Args:
            tool_name: 工具名称
            arguments: 工具参数
            
        Returns:
            工具执行结果
        """
        # 找到工具对应的服务器
        tool_key = None
        server_id = None
        
        for key, tool in self.tools.items():
            if tool.name == tool_name:
                tool_key = key
                server_id = key.split(':')[0]
                break
        
        if not tool_key or not server_id:
            return {
                "status": "error",
                "error": f"工具 {tool_name} 未找到"
            }
        
        server_config = self.servers.get(server_id)
        if not server_config:
            return {
                "status": "error",
                "error": f"服务器 {server_id} 配置未找到"
            }
        
        try:
            if server_config.type == 'stdio':
                return await self._call_stdio_tool(server_config, tool_name, arguments)
            elif server_config.type == 'streamableHttp':
                return await self._call_http_tool(server_config, tool_name, arguments)
            else:
                return {
                    "status": "error",
                    "error": f"不支持的服务器类型: {server_config.type}"
                }
                
        except Exception as e:
            return {
                "status": "error",
                "error": f"工具调用失败: {str(e)}"
            }
    
    async def _call_stdio_tool(self, server_config: McpServerConfig, tool_name: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """
        调用stdio类型服务器的工具
        
        Args:
            server_config: 服务器配置
            tool_name: 工具名称
            arguments: 工具参数
            
        Returns:
            工具执行结果
        """
        process = self.server_processes.get(server_config.server_id)
        if not process:
            return {
                "status": "error",
                "error": "服务器进程未启动"
            }
        
        try:
            # 构建工具调用请求
            request = {
                "jsonrpc": "2.0",
                "id": 2,
                "method": "tools/call",
                "params": {
                    "name": tool_name,
                    "arguments": arguments
                }
            }
            
            request_json = json.dumps(request) + "\n"
            process.stdin.write(request_json)
            process.stdin.flush()
            
            # 读取响应
            response_line = process.stdout.readline()
            if response_line:
                response = json.loads(response_line.strip())
                
                if 'result' in response:
                    return {
                        "status": "success",
                        "result": response['result']
                    }
                elif 'error' in response:
                    return {
                        "status": "error",
                        "error": response['error']
                    }
            
            return {
                "status": "error",
                "error": "无响应"
            }
            
        except Exception as e:
            return {
                "status": "error",
                "error": f"stdio工具调用失败: {str(e)}"
            }
    
    async def _call_http_tool(self, server_config: McpServerConfig, tool_name: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """
        调用HTTP类型服务器的工具
        
        Args:
            server_config: 服务器配置
            tool_name: 工具名称
            arguments: 工具参数
            
        Returns:
            工具执行结果
        """
        try:
            async with aiohttp.ClientSession() as session:
                # 构建工具调用请求
                request_data = {
                    "jsonrpc": "2.0",
                    "id": 2,
                    "method": "tools/call",
                    "params": {
                        "name": tool_name,
                        "arguments": arguments
                    }
                }
                
                async with session.post(
                    server_config.base_url,
                    json=request_data,
                    headers={
                        'Content-Type': 'application/json',
                        'Accept': 'application/json, text/event-stream'
                    }
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        
                        if 'result' in result:
                            return {
                                "status": "success",
                                "result": result['result']
                            }
                        elif 'error' in result:
                            return {
                                "status": "error",
                                "error": result['error']
                            }
                    else:
                        return {
                            "status": "error",
                            "error": f"HTTP请求失败: {response.status}"
                        }
                        
        except Exception as e:
            return {
                "status": "error",
                "error": f"HTTP工具调用失败: {str(e)}"
            }
    
    def get_tools_summary(self) -> Dict[str, Any]:
        """
        获取工具摘要信息
        
        Returns:
            工具摘要
        """
        return {
            "servers_count": len(self.servers),
            "tools_count": len(self.tools),
            "servers": {server_id: str(config) for server_id, config in self.servers.items()},
            "tools": {tool_key: str(tool) for tool_key, tool in self.tools.items()}
        }
    
    def cleanup(self):
        """
        清理资源，关闭所有进程
        """
        for server_id, process in self.server_processes.items():
            try:
                process.terminate()
                process.wait(timeout=5)
            except Exception as e:
                self.logger.error(f"关闭服务器进程 {server_id} 失败: {str(e)}")
        
        self.server_processes.clear()


# 使用示例
if __name__ == "__main__":
    import asyncio
    
    async def main():
        # 创建MCP客户端
        mcp_client = McpClient()
        
        print("=== MCP Client 测试 ===")
        print(f"加载了 {len(mcp_client.servers)} 个MCP服务器")
        
        # 发现工具
        print("\n发现工具中...")
        success = await mcp_client.discover_tools()
        
        if success:
            print(f"\n发现了 {len(mcp_client.tools)} 个工具:")
            for tool_key, tool in mcp_client.tools.items():
                print(f"  - {tool}")
            
            # 获取OpenAI格式的工具
            print("\n=== OpenAI Function Call 格式 ===")
            openai_functions = mcp_client.get_openai_functions()
            for func in openai_functions:
                print(f"工具: {func['function']['name']}")
                print(f"描述: {func['function']['description']}")
                print(f"参数: {func['function']['parameters']}")
                print("-" * 40)
            
            # 测试工具调用（如果有工具的话）
            if mcp_client.tools:
                print("\n=== 测试工具调用 ===")
                first_tool = list(mcp_client.tools.values())[0]
                print(f"测试调用工具: {first_tool.name}")
                
                # 这里需要根据具体工具的参数schema来构建测试参数
                test_args = {}  # 空参数测试
                result = await mcp_client.call_tool(first_tool.name, test_args)
                print(f"调用结果: {result}")
        else:
            print("工具发现失败")
        
        # 显示摘要
        print("\n=== 工具摘要 ===")
        summary = mcp_client.get_tools_summary()
        print(json.dumps(summary, indent=2, ensure_ascii=False))
        
        # 清理资源
        mcp_client.cleanup()
    
    # 运行测试
    asyncio.run(main())

