{"mcpServers": {"NXZZFTrev8d2C4aoQ2Xmj": {"name": "local memory", "type": "stdio", "description": "本地数据库， 用于处理agent运行中长期记忆的存取", "isActive": false, "registryUrl": "", "command": "uv", "args": ["--directory", "/Users/<USER>/Documents/agent相关/practice project/mcp-mem0/mcp-mem0/src", "run", "main.py"]}, "amap-maps-streamableHTTP": {"name": "amap-maps-streamableHTTP", "type": "streamableHttp", "isActive": true, "baseUrl": "https://mcp.amap.com/mcp?key=7577a5470ca74e0d818841103c5e7b7f"}}}