# Think Tool: https://www.anthropic.com/engineering/claude-think-tool
# 这是一个本地模拟 MCP Server 的 Think Tool
# 提供思考和推理能力，用于复杂推理或缓存记忆

import logging
from datetime import datetime
from typing import Dict, Any


# 全局思考记录列表
thoughts_log = []
logger = logging.getLogger(__name__)


def get_think_tool_schema() -> Dict[str, Any]:
    """
    获取 Think Tool 的 OpenAI Function Call Schema
    
    Returns:
        Dict[str, Any]: 工具 schema
    """
    return {
        "type": "function",
        "function": {
            "name": "think",
            "description": "Use the tool to think about something. It will not obtain new information or change the database, but just append the thought to the log. Use it when complex reasoning or some cache memory is needed.",
            "parameters": {
                "type": "object",
                "properties": {
                    "thought": {
                        "type": "string",
                        "description": "A thought to think about."
                    }
                },
                "required": ["thought"]
            }
        }
    }


def think_tool(thought: str) -> Dict[str, Any]:
    """
    Think Tool 主要方法 - 记录思考内容
    
    Args:
        thought: 要思考的内容
        
    Returns:
        Dict[str, Any]: 思考结果
    """
    try:
        # 记录思考内容
        thought_entry = {
            "timestamp": datetime.now().isoformat(),
            "thought": thought,
            "id": len(thoughts_log) + 1
        }
        
        thoughts_log.append(thought_entry)
        
        # 记录日志
        logger.info(f"Think Tool - 思考记录: {thought}")
        
        return {
            "status": "success",
            "message": "思考已记录",
            "thought_id": thought_entry["id"],
            "timestamp": thought_entry["timestamp"],
            "content": thought
        }
        
    except Exception as e:
        logger.error(f"Think Tool 执行错误: {str(e)}")
        return {
            "status": "error",
            "error": f"思考工具执行失败: {str(e)}"
        }


if __name__ == "__main__":
    # 测试 Think Tool
    import json
    
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    print("=== Think Tool 测试 ===")
    
    # 获取工具 schema
    schema = get_think_tool_schema()
    print(f"\n工具 Schema: {json.dumps(schema, indent=2, ensure_ascii=False)}")
    
    # 测试思考功能
    print("\n=== 测试思考功能 ===")
    result1 = think_tool("我需要分析用户的问题类型")
    print(f"思考结果1: {result1}")
    
    result2 = think_tool("这个问题涉及地理位置查询，应该使用GeoAgent")
    print(f"思考结果2: {result2}")
    
    result3 = think_tool("需要考虑错误处理和异常情况")
    print(f"思考结果3: {result3}")
    
    print(f"\n总共记录了 {len(thoughts_log)} 条思考")
    print("\n=== 测试完成 ===")

