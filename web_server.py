#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Agentic RAG System - Web Server
=====================================

这是一个基于多Agent协作的RAG（检索增强生成）系统的Web服务器。

系统架构：
- MainController：主控制器，协调整个系统执行流程
- 多Agent协作：PlanAgent（规划）、MemoryAgent（记忆）、WebSearchAgent（搜索）、
                GeoAgent（地理信息）、GenImgAgent（图像生成）
- 短期内存池：支持引用解析和上下文管理
- 流式响应：支持SSE（Server-Sent Events）实时返回执行进度
- 图像代理：解决CORS问题，支持图像显示

主要功能：
1. 用户问题分析和规划
2. 多步骤任务执行
3. 变量引用和上下文传递
4. 短期内存管理
5. 实时流式响应
6. 图像生成和编辑
"""

import asyncio
import json
import logging
import sys
import os
from datetime import datetime
from typing import Dict, Any, Optional, Callable, List
from flask import Flask, Response, request, send_from_directory
from flask_cors import CORS
import threading
import queue
import time
import re
from threading import Lock

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入各个Agent模块
from agent.PlanAgent import PlanAgent, AgentType
from agent.MemoryAgent import MemoryAgent
from agent.WebSearchAgent import WebSearchAgent
from agent.GeoAgent import GeoAgent
from agent.GenImgAgent import GenImgAgent
from util.short_term_memory import ShortTermMemoryPool, get_memory_pool

# 配置日志系统
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 创建Flask应用和CORS配置
app = Flask(__name__)
CORS(app)  # 允许跨域请求，支持前端调用

# 简单的图片缓存
image_cache = {}
image_cache_max_size = 100

def cleanup_image_cache():
    """清理过期的图片缓存"""
    current_time = time.time()
    cache_ttl = 3600  # 1小时过期
    
    keys_to_remove = []
    for key, value in image_cache.items():
        if current_time - value['timestamp'] > cache_ttl:
            keys_to_remove.append(key)
    
    for key in keys_to_remove:
        del image_cache[key]
        logger.info(f"清理过期缓存: {key}")
    
    if keys_to_remove:
        logger.info(f"清理了 {len(keys_to_remove)} 个过期缓存项")

class MainController:
    """
    主控制器 - 协调整个系统的执行流程
    
    执行流程：
    1. 调用PlanAgent创建执行计划
    2. 按步骤执行对应的Agent
    3. 生成最终答案
    """
    
    def __init__(self):
        """
        初始化主控制器
        """
        self.plan_agent = PlanAgent()
        self.memory_agent = MemoryAgent()
        self.web_search_agent = WebSearchAgent()
        self.geo_agent = GeoAgent()
        self.gen_img_agent = GenImgAgent()
        
        self.logger = logging.getLogger(__name__)
        self.memory_pool = get_memory_pool()  # 短期内存池
        
        # Agent映射
        self.agent_map = {
            AgentType.MEMORY: self.memory_agent,
            AgentType.WEB_SEARCH: self.web_search_agent,
            AgentType.GEO: self.geo_agent,
            AgentType.GEN_IMG: self.gen_img_agent
        }
    
    async def initialize(self) -> bool:
        """
        初始化所有Agent
        
        Returns:
            是否初始化成功
        """
        try:
            self.logger.info("开始初始化MainController...")
            
            # 初始化各个Agent
            memory_init = await self.memory_agent.initialize()
            geo_init = await self.geo_agent.initialize()
            
            # WebSearchAgent不需要异步初始化
            web_init = True
            
            if memory_init and geo_init and web_init:
                self.logger.info("MainController初始化成功")
                return True
            else:
                self.logger.error(f"Agent初始化失败: Memory={memory_init}, Geo={geo_init}, Web={web_init}")
                return False
                
        except Exception as e:
            self.logger.error(f"MainController初始化异常: {str(e)}")
            return False
    
    async def process_user_question(self, user_question: str) -> Dict[str, Any]:
        """
        处理用户问题的主入口
        
        Args:
            user_question: 用户问题
            
        Returns:
            处理结果
        """
        try:
            self.logger.info(f"开始处理用户问题: {user_question}")
            
            # 1. 添加用户消息到短期内存
            self.memory_pool.add_user_message(user_question)
            
            # 2. 检查是否有引用需要解析
            reference_result = self.memory_pool.resolve_reference(user_question)
            if reference_result:
                self.logger.info(f"发现引用: {reference_result['resolved_text']}")
                # 在问题中添加引用上下文
                user_question = self._enhance_question_with_reference(user_question, reference_result)
            
            # 3. 执行一次完整的流程
            result = await self._execute_once(user_question, is_retry=False)
            
            return result
            
        except Exception as e:
            self.logger.error(f"处理用户问题时发生错误: {e}")
            return {
                "status": "error",
                "error": str(e),
                "final_answer": "抱歉，处理您的问题时遇到了错误，请稍后重试。"
            }

    async def _execute_once(self, user_question: str, is_retry: bool = False) -> Dict[str, Any]:
        """
        执行一次完整的规划和执行流程
        
        Args:
            user_question: 用户问题
            is_retry: 是否为重试执行
            
        Returns:
            执行结果
        """
        try:
            execution_type = "重新规划执行" if is_retry else "首次执行"
            self.logger.info(f"开始{execution_type}: {user_question}")
            
            # 1. 分析用户问题
            analysis_result = self.plan_agent.analyze_user_question(user_question)
            
            # 安全获取建议的助手类型
            steps = analysis_result.get('execution_plan', {}).get('steps', [])
            suggested_assistant = steps[0].get('assistant', 'Unknown') if steps else 'Unknown'
            self.logger.info(f"问题分析完成，建议使用: {suggested_assistant}")
            
            # 2. 基于分析结果创建执行计划
            execution_plan = self.plan_agent.create_execution_plan(user_question, analysis_result)
            
            self.logger.info(f"创建执行计划成功， 步骤数: {len(execution_plan.steps)}")
            
            # 3. 执行各个步骤
            step_results = []
            step_outputs = {}  # 存储步骤输出，用于变量引用
            
            for step in execution_plan.steps:
                self.logger.info(f"执行步骤: {step.step_id} - {step.task_description}")
                
                # 检查依赖是否满足
                if not self._check_dependencies(step, step_outputs):
                    error_msg = f"步骤 {step.step_id} 的依赖未满足: {step.dependencies}"
                    self.logger.error(error_msg)
                    return {
                        "status": "error",
                        "error": error_msg,
                        "execution_plan": execution_plan.to_dict(),
                        "step_results": step_results
                    }
                
                # 处理输入数据中的变量引用
                processed_input = self._process_step_input(step.input_data, step_outputs)
                
                # 执行步骤
                step_result = await self._execute_step(step, processed_input)
                
                # 更新步骤状态
                self.logger.debug(f"处理步骤结果: {step.step_id}, step_result类型: {type(step_result)}, 内容: {step_result}")
                
                # 检查step_result的类型
                if not isinstance(step_result, dict):
                    self.logger.error(f"步骤结果不是字典类型: {type(step_result)}, 内容: {step_result}")
                    return {
                        "status": "error",
                        "error": f"步骤 {step.step_id} 返回了无效的结果类型: {type(step_result)}",
                        "execution_plan": execution_plan.to_dict(),
                        "step_results": step_results
                    }
                
                if step_result.get("status") == "success":
                    result_content = step_result.get("result", "")
                    self.plan_agent.update_step_status(step.step_id, "completed", str(result_content))
                    # 存储步骤输出 - 存储完整的step_result，保持数据结构一致性
                    step_outputs[step.step_id] = step_result
                    self.logger.info(f"步骤 {step.step_id} 成功，存储输出结构: {type(step_result)}")
                    self.logger.info(f"步骤 {step.step_id} 输出内容: {json.dumps(step_result, ensure_ascii=False, indent=2) if isinstance(step_result, dict) else str(step_result)}")
                else:
                    error_content = step_result.get("error", "")
                    self.plan_agent.update_step_status(step.step_id, "failed", str(error_content))
                    # 步骤失败，返回错误
                    return {
                        "status": "error",
                        "error": f"步骤 {step.step_id} 执行失败: {error_content}",
                        "execution_plan": execution_plan.to_dict(),
                        "step_results": step_results + [step_result]
                    }
                
                step_results.append(step_result)
                self.logger.info(f"步骤 {step.step_id} 执行完成")
            
            # 4. 生成最终答案
            final_answer = self._generate_final_answer(user_question, step_results, step_outputs)
            
            # 5. 将结果添加到短期内存
            self._add_result_to_memory(final_answer, step_results)
            
            # 6. 生成执行摘要
            execution_summary = self._generate_execution_summary(execution_plan, step_results)
            
            self.logger.info(f"{execution_type}完成")
            
            return {
                "status": "success",
                "final_answer": final_answer,
                "execution_plan": execution_plan.to_dict(),
                "step_results": step_results,
                "execution_summary": execution_summary
            }
            
        except Exception as e:
            self.logger.error(f"{execution_type}异常: {str(e)}")
            return {
                "status": "error",
                "error": f"{execution_type}异常: {str(e)}"
            }
    
    def _check_dependencies(self, step, step_outputs: Dict[str, Any]) -> bool:
        """
        检查步骤依赖是否满足
        
        Args:
            step: 当前步骤
            step_outputs: 已完成步骤的输出
            
        Returns:
            依赖是否满足
        """
        for dep_id in step.dependencies:
            if dep_id not in step_outputs:
                return False
            if step_outputs[dep_id].get("status") != "success":
                return False
        return True
    
    def _enhance_question_with_reference(self, user_question: str, reference_result: Dict[str, Any]) -> str:
        """
        使用引用信息增强用户问题
        
        当用户使用"上图"、"刚才的图片"等引用词时，此方法会：
        1. 从短期内存中提取引用的具体内容
        2. 将引用内容添加到用户问题中作为上下文
        3. 指导PlanAgent不要再调用MemoryAgent查询
        
        Args:
            user_question: 原始用户问题
            reference_result: 引用解析结果
            
        Returns:
            增强后的用户问题（包含引用上下文）
        """
        item = reference_result["item"]
        
        if reference_result["type"] == "exact_id":
            # 精确ID引用，添加具体信息
            artifact = item.artifact
            enhancement = f"\n\n[引用上下文已解析] {reference_result['artifact_id']}: {item.content}"
            if artifact.get("url"):
                enhancement += f"\n图片URL: {artifact['url']}"
            if artifact.get("description"):
                enhancement += f"\n描述: {artifact['description']}"
        else:
            # 自然语言引用，添加描述
            artifact = item.artifact
            enhancement = f"\n\n[引用上下文已解析] 用户引用的是最近的{artifact.get('type', '图片')}内容"
            if artifact.get("url"):
                enhancement += f"\n图片URL: {artifact['url']}"
            # 只包含关键信息，避免传递过多内容
            if artifact.get("description"):
                enhancement += f"\n描述: {artifact['description']}"
        
        enhanced_question = user_question + enhancement
        enhanced_question += "\n\n[规划指导] 引用内容已经解析完成，请不要再调用MemoryAgent查询，直接基于上述引用内容和用户需求制定执行计划。"
        
        return enhanced_question
    
    def _add_result_to_memory(self, final_answer: str, step_results: List[Dict[str, Any]]):
        """
        将执行结果添加到短期内存
        
        此方法负责：
        1. 解析步骤结果中的生成物（图片、文本等）
        2. 为生成物分配ID，便于后续引用
        3. 将助手回复和生成物存储到短期内存池
        4. 支持多种类型的生成物：图片、搜索结果、地理信息等
        
        Args:
            final_answer: 最终回答文本
            step_results: 各步骤的执行结果列表
        """
        try:
            # 检查步骤结果中是否有生成物（特别是图片）
            artifacts = []
            
            for result in step_results:
                if result.get("status") == "success":
                    # 检查GenImgAgent的结果
                    if "image_urls" in result and result["image_urls"]:
                        for i, url in enumerate(result["image_urls"]):
                            artifact = {
                                "type": "image",
                                "url": url,
                                "description": result.get("message", "生成的图片"),
                                "task_type": result.get("task_type", "unknown")
                            }
                            artifacts.append(artifact)
                    
                    # 检查其他类型的结果
                    elif result.get("agent_type") == "memory":
                        # 检查是否有短期内存的artifact信息
                        if result.get("source") == "short_term_memory" and result.get("artifact_info"):
                            artifact_info = result["artifact_info"]
                            if artifact_info.get("type") == "image":
                                # 如果是从短期内存找到的图片，不重复添加，只记录查询结果
                                artifact = {
                                    "type": "memory_query_result",
                                    "content": result.get("result", ""),
                                    "description": "短期内存查询结果",
                                    "referenced_artifact_id": artifact_info.get("id")
                                }
                            else:
                                artifact = {
                                    "type": "memory_query_result",
                                    "content": result.get("result", ""),
                                    "description": "短期内存查询结果"
                                }
                        else:
                            artifact = {
                                "type": "memory_record", 
                                "content": result.get("result", ""),
                                "description": "长期记忆查询结果"
                            }
                        artifacts.append(artifact)
                    
                    elif result.get("agent_type") == "web_search":
                        artifact = {
                            "type": "search_result",
                            "content": result.get("result", ""),
                            "description": "网络搜索结果" 
                        }
                        artifacts.append(artifact)
                    
                    elif result.get("agent_type") == "geo":
                        artifact = {
                            "type": "geo_info",
                            "content": result.get("result", ""),
                            "description": "地理信息查询结果"
                        }
                        artifacts.append(artifact)
            
            # 添加助手回复到内存
            if artifacts:
                # 如果有多个artifacts，为每个单独添加
                for artifact in artifacts:
                    item_id = self.memory_pool.add_assistant_message(final_answer, artifact)
                    self.logger.info(f"添加生成物到短期内存: {artifact['type']} (ID: {artifact.get('id')})")
            else:
                # 没有artifacts，只添加文本回复
                item_id = self.memory_pool.add_assistant_message(final_answer)
                self.logger.info(f"添加文本回复到短期内存")
            
        except Exception as e:
            self.logger.error(f"添加结果到短期内存失败: {e}")
    
    def _process_step_input(self, input_data, step_outputs: Dict[str, Any]) -> str:
        """
        处理步骤输入中的变量引用
        
        Args:
            input_data: 原始输入数据（可能是字符串或字典）
            step_outputs: 步骤输出数据
            
        Returns:
            处理后的输入数据（JSON字符串格式）
        """
        try:
            # 如果输入已经是字典，直接处理变量引用
            if isinstance(input_data, dict):
                processed_dict = self._replace_variables_in_dict(input_data, step_outputs)
                return json.dumps(processed_dict, ensure_ascii=False)
            # 如果输入是JSON字符串，尝试解析并处理变量引用
            elif isinstance(input_data, str) and input_data.strip().startswith('{'):
                try:
                    input_dict = json.loads(input_data)
                except json.JSONDecodeError:
                    # 如果JSON解析失败，尝试使用eval（仅对字典格式字符串）
                    try:
                        import ast
                        input_dict = ast.literal_eval(input_data)
                    except:
                        # 如果都失败了，当作普通字符串处理
                        return self._replace_variables_in_string(str(input_data), step_outputs)
                processed_dict = self._replace_variables_in_dict(input_dict, step_outputs)
                return json.dumps(processed_dict, ensure_ascii=False)
            else:
                # 简单字符串，直接处理变量引用
                return self._replace_variables_in_string(str(input_data), step_outputs)
        except Exception as e:
            self.logger.error(f"处理步骤输入时出错: {e}, input_data类型: {type(input_data)}, 内容: {input_data}")
            # 如果处理失败，返回原始数据的字符串形式
            if isinstance(input_data, dict):
                return json.dumps(input_data, ensure_ascii=False)
            else:
                return str(input_data)
    
    def _replace_variables_in_dict(self, data: Dict[str, Any], step_outputs: Dict[str, Any]) -> Dict[str, Any]:
        """
        递归替换字典中的变量引用
        """
        result = {}
        for key, value in data.items():
            if isinstance(value, str):
                result[key] = self._replace_variables_in_string(value, step_outputs)
            elif isinstance(value, dict):
                result[key] = self._replace_variables_in_dict(value, step_outputs)
            elif isinstance(value, list):
                result[key] = [self._replace_variables_in_string(str(item), step_outputs) if isinstance(item, str) else item for item in value]
            else:
                result[key] = value
        return result
    
    def _replace_variables_in_string(self, text: str, step_outputs: Dict[str, Any]) -> str:
        """
        替换字符串中的变量引用
        
        支持多种变量引用格式：
        - ${S1.result}: 美元符号格式
        - {{S1.result}}: 双大括号格式  
        - {S1.result}: 单大括号格式
        
        变量引用规则：
        - 只支持 {步骤ID.result} 格式
        - result字段包含Agent的完整输出内容
        - 不支持嵌套属性访问如 {S1.result.url}
        
        Args:
            text: 包含变量引用的文本
            step_outputs: 步骤输出字典，键为步骤ID
            
        Returns:
            替换变量后的文本
        """
        original_text = text
        
        def replace_var(match):
            var_ref = match.group(1)  # 获取${...}或{{...}}中的内容
            self.logger.debug(f"处理变量引用: {var_ref}")
            
            if '.' in var_ref:
                step_id, key = var_ref.split('.', 1)
                self.logger.debug(f"解析变量引用 - step_id: {step_id}, key: {key}")
                
                if step_id in step_outputs:
                    step_output = step_outputs[step_id]
                    self.logger.debug(f"找到step_output: {type(step_output)} - {step_output}")
                    
                    # 如果step_output是字符串，直接返回
                    if isinstance(step_output, str):
                        self.logger.debug(f"step_output是字符串，直接返回: {step_output}")
                        return step_output
                    # 如果是字典，按照统一的Agent输出格式获取数据
                    elif isinstance(step_output, dict):
                        self.logger.debug(f"step_output是字典，尝试获取key: {key}")
                        try:
                            # 按照统一格式，直接从result字段获取数据
                            if key == 'result' and 'result' in step_output:
                                result = str(step_output['result'])
                                self.logger.debug(f"获取到result字段值: {result}")
                                return result
                            # 如果是其他字段，直接从step_output获取
                            elif key in step_output:
                                result = str(step_output[key])
                                self.logger.debug(f"直接从step_output获取到值: {result}")
                                return result
                            else:
                                self.logger.debug(f"未找到key: {key}，返回原始引用")
                                return match.group(0)
                        except Exception as e:
                            self.logger.error(f"处理字典step_output时出错: {e}, step_output类型: {type(step_output)}, 内容: {step_output}")
                            return match.group(0)
                    else:
                        self.logger.warning(f"step_output类型未知: {type(step_output)} - {step_output}")
                else:
                    self.logger.debug(f"未找到step_id: {step_id} in step_outputs")
            else:
                # 没有点号，直接引用step_id
                self.logger.debug(f"直接引用step_id: {var_ref}")
                if var_ref in step_outputs:
                    step_output = step_outputs[var_ref]
                    self.logger.debug(f"找到直接引用的step_output: {type(step_output)} - {step_output}")
                    try:
                        result = str(step_output)
                        self.logger.debug(f"转换为字符串: {result}")
                        return result
                    except Exception as e:
                        self.logger.error(f"转换step_output为字符串时出错: {e}, step_output类型: {type(step_output)}, 内容: {step_output}")
                        return match.group(0)
                else:
                    self.logger.debug(f"未找到直接引用的step_id: {var_ref}")
            
            self.logger.debug(f"变量引用未找到，返回原始文本: {match.group(0)}")
            return match.group(0)  # 如果找不到，返回原始文本
        
        try:
            self.logger.info(f"开始处理文本变量引用: {text}")
            self.logger.info(f"当前step_outputs内容: {json.dumps(step_outputs, ensure_ascii=False, indent=2)}")
            # 匹配${...}、{{...}}和{...}格式的变量引用
            result = re.sub(r'\$\{([^}]+)\}', replace_var, text)
            result = re.sub(r'\{\{([^}]+)\}\}', replace_var, result)
            result = re.sub(r'\{([^}]+)\}', replace_var, result)
            
            if original_text != result:
                self.logger.info(f"变量替换完成: {original_text} -> {result}")
            else:
                self.logger.info(f"无变量需要替换: {text}")
                
            return result
        except Exception as e:
            self.logger.error(f"_replace_variables_in_string处理过程中出错: {e}, 原始文本: {text}")
            return text
    
    async def _execute_step(self, step, processed_input: str) -> Dict[str, Any]:
        """
        执行单个步骤
        
        Args:
            step: 步骤对象
            processed_input: 处理后的输入数据
            
        Returns:
            执行结果
        """
        try:
            # 获取对应的Agent
            agent = self.agent_map.get(step.agent_type)
            if not agent:
                return {
                    "status": "error",
                    "error": f"未找到对应的Agent: {step.agent_type}",
                    "step_id": step.step_id
                }
            
            # 构建任务输入
            task_input = f"任务描述: {step.task_description}\n输入数据: {processed_input}"
            
            # 添加调试日志
            self.logger.info(f"步骤 {step.step_id} 执行前 - 原始输入: {step.input_data}")
            self.logger.info(f"步骤 {step.step_id} 执行前 - 处理后输入: {processed_input}")
            self.logger.info(f"步骤 {step.step_id} 执行前 - 最终task_input长度: {len(task_input)}")
            self.logger.info(f"步骤 {step.step_id} 执行前 - task_input内容: {repr(task_input)}")
            
            # 调用Agent执行任务
            result = await agent.process_task(task_input)
            
            self.logger.debug(f"Agent返回结果类型: {type(result)}, 内容: {result}")
            
            # 检查result的类型并处理
            if isinstance(result, dict):
                # 添加步骤信息
                result["step_id"] = step.step_id
                result["agent_type"] = step.agent_type.value
                result["task_description"] = step.task_description
                return result
            else:
                # 如果result不是字典，包装成标准格式
                self.logger.warning(f"Agent返回了非字典类型结果: {type(result)}, 包装为标准格式")
                return {
                    "status": "success",
                    "result": str(result),
                    "step_id": step.step_id,
                    "agent_type": step.agent_type.value,
                    "task_description": step.task_description
                }
            
        except Exception as e:
            return {
                "status": "error",
                "error": f"步骤执行异常: {str(e)}",
                "step_id": step.step_id,
                "agent_type": step.agent_type.value
            }
    
    def _generate_final_answer(self, user_question: str, step_results: List[Dict[str, Any]], step_outputs: Dict[str, Any]) -> str:
        """
        生成最终答案 - 简化版本，直接收集数据传递给LLM分析总结
        
        Args:
            user_question: 用户问题
            step_results: 步骤执行结果
            step_outputs: 步骤输出数据
            
        Returns:
            最终答案
        """
        try:
            # 简单收集所有成功的步骤结果
            subtasks_results = []
            for i, result in enumerate(step_results):
                if result.get("status") == "success":
                    # 提取步骤信息
                    step_id = result.get("step_id", f"S{i+1}")
                    task_description = result.get("task_description", "未知任务")
                    
                    # 提取答案内容，特别处理MemoryAgent和GenImgAgent的返回格式
                    answer = ""
                    
                    # 首先检查是否是GenImgAgent的直接返回格式（包含image_urls）
                    if "image_urls" in result:
                        image_urls = result["image_urls"]
                        if isinstance(image_urls, list) and image_urls:
                            # 转换为Markdown格式的图片链接
                            markdown_images = []
                            for i, url in enumerate(image_urls):
                                markdown_images.append(f"![生成的图片{i+1}]({url})")
                            answer = f"图像生成成功！\n\n{chr(10).join(markdown_images)}"
                            self.logger.info(f"生成的Markdown图片格式: {answer}")
                        else:
                            answer = "图片生成完成"
                    else:
                        # 其他情况，从result字段获取
                        answer = str(result.get("result", ""))
                    
                    subtasks_results.append({
                        "step_id": step_id,
                        "task": task_description,
                        "answer": answer
                    })
            
            # 如果没有成功的步骤，返回错误信息
            if not subtasks_results:
                return "抱歉，所有执行步骤都失败了，无法生成答案。"
            
            # 构建最终答案
            final_answer_parts = []
            final_answer_parts.append(f"针对您的问题：{user_question}")
            final_answer_parts.append("\n执行结果：")
            
            for result in subtasks_results:
                final_answer_parts.append(f"\n• {result['task']}：{result['answer']}")
            
            return "\n".join(final_answer_parts)
            
        except Exception as e:
            self.logger.error(f"生成最终答案时发生错误: {e}")
            return f"生成答案时发生错误: {str(e)}"
    
    def _generate_execution_summary(self, execution_plan, step_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        生成执行摘要
        
        Args:
            execution_plan: 执行计划
            step_results: 步骤结果
            
        Returns:
            执行摘要
        """
        try:
            total_steps = len(execution_plan.steps)
            successful_steps = sum(1 for result in step_results if result.get("status") == "success")
            failed_steps = total_steps - successful_steps
            
            # 统计使用的Agent类型
            agents_used = list(set(step.agent_type.value for step in execution_plan.steps))
            
            # 计算总数据处理量（简单统计）
            total_data_processed = sum(len(str(result.get("result", ""))) for result in step_results)
            
            return {
                "total_steps": total_steps,
                "successful_steps": successful_steps,
                "failed_steps": failed_steps,
                "success_rate": successful_steps / total_steps if total_steps > 0 else 0,
                "agents_used": agents_used,
                "total_data_processed": total_data_processed,
                "execution_time": datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"生成执行摘要时发生错误: {e}")
            return {
                "error": f"生成摘要失败: {str(e)}",
                "execution_time": datetime.now().isoformat()
            }

# =====================================
# 全局变量定义
# =====================================

# 全局MainController实例 - 在服务启动时初始化，避免每次请求重复初始化
main_controller = None

# 线程锁 - 确保并发请求时controller复制的线程安全
controller_lock = Lock()

class StreamingController:
    """
    流式响应控制器
    
    负责管理SSE（Server-Sent Events）流式响应：
    1. 维护事件队列，缓存待发送的事件
    2. 控制处理状态，管理流式响应的生命周期
    3. 提供事件发送和获取接口
    4. 支持心跳包保持连接活跃
    """
    
    def __init__(self):
        self.event_queue = queue.Queue()  # 事件队列，线程安全
        self.is_processing = False        # 处理状态标志
        
    def emit_event(self, event_type: str, content: Any, **kwargs):
        """发送事件到前端"""
        event_data = {
            'type': event_type,
            'content': content,
            'timestamp': datetime.now().isoformat(),
            **kwargs
        }
        
        try:
            self.event_queue.put(json.dumps(event_data, ensure_ascii=False), timeout=1)
        except queue.Full:
            logger.warning("事件队列已满，丢弃事件")
    
    def get_events(self):
        """获取事件流"""
        while self.is_processing or not self.event_queue.empty():
            try:
                event = self.event_queue.get(timeout=0.1)
                yield f"data: {event}\n\n"
            except queue.Empty:
                # 发送心跳包保持连接
                yield f"data: {{\"type\": \"heartbeat\", \"timestamp\": \"{datetime.now().isoformat()}\"}}\n\n"
                time.sleep(0.1)
        
        # 发送完成事件
        self.emit_event('complete', '处理完成')
        final_event = self.event_queue.get()
        yield f"data: {final_event}\n\n"

class EnhancedMainController(MainController):
    """增强的MainController，支持流式回调"""
    
    def __init__(self, streaming_controller: StreamingController):
        super().__init__()
        self.streaming_controller = streaming_controller
        
    async def process_user_question_with_streaming(self, user_question: str) -> Dict[str, Any]:
        """带流式输出的问题处理"""
        try:
            self.logger.info(f"开始处理用户问题: {user_question}")
            
            # 1. 添加用户消息到短期内存
            self.memory_pool.add_user_message(user_question)
            
            # 2. 检查是否有引用需要解析
            reference_result = self.memory_pool.resolve_reference(user_question)
            if reference_result:
                self.logger.info(f"发现引用: {reference_result['resolved_text']}")
                # 在问题中添加引用上下文
                user_question = self._enhance_question_with_reference(user_question, reference_result)
            
            # 3. 执行一次完整的流程
            result = await self._execute_once_with_streaming(user_question, is_retry=False)
            
            return result
            
        except Exception as e:
            self.logger.error(f"处理用户问题时发生错误: {e}")
            return {
                "status": "error",
                "error": str(e),
                "final_answer": "抱歉，处理您的问题时遇到了错误，请稍后重试。"
            }

    
    async def _execute_once_with_streaming(self, user_question: str, is_retry: bool = False) -> Dict[str, Any]:
        """带流式输出的执行流程"""
        try:
            execution_type = "重新规划执行" if is_retry else "首次执行"
            self.logger.info(f"开始{execution_type}: {user_question}")
            
            # 1. 分析用户问题
            self.streaming_controller.emit_event('analysis', f'正在分析问题: {user_question}')
            analysis_result = self.plan_agent.analyze_user_question(user_question)
            
            # 发送分析结果
            analysis_content = f"问题类型: {analysis_result.get('question_type', 'Unknown')}\n"
            analysis_content += f"建议Agent: {analysis_result.get('execution_plan', {}).get('steps', [{}])[0].get('assistant', 'Unknown')}\n"
            analysis_content += f"分析说明: {analysis_result.get('reasoning', '无')}"
            self.streaming_controller.emit_event('analysis', analysis_content)
            
            # 2. 基于分析结果创建执行计划
            self.streaming_controller.emit_event('plan', '正在创建执行计划...')
            execution_plan = self.plan_agent.create_execution_plan(user_question, analysis_result)
            
            # 发送执行计划
            plan_content = f"执行步骤数: {len(execution_plan.steps)}\n\n"
            for i, step in enumerate(execution_plan.steps, 1):
                plan_content += f"步骤{i}: {step.task_description}\n"
                plan_content += f"  - Agent类型: {step.agent_type}\n"
                plan_content += f"  - 输入数据: {step.input_data[:100]}{'...' if len(step.input_data) > 100 else ''}\n\n"
            
            self.streaming_controller.emit_event('plan', plan_content)
            self.logger.info(f"创建执行计划成功， 步骤数: {len(execution_plan.steps)}")
            
            # 3. 执行各个步骤
            step_results = []
            step_outputs = {}  # 存储步骤输出，用于变量引用
            
            for step in execution_plan.steps:
                self.logger.info(f"执行步骤: {step.step_id} - {step.task_description}")
                
                # 发送步骤开始事件
                self.streaming_controller.emit_event(
                    'step_start', 
                    step.task_description,
                    step_id=step.step_id,
                    description=step.task_description
                )
                
                # 检查依赖是否满足
                if not self._check_dependencies(step, step_outputs):
                    error_msg = f"步骤 {step.step_id} 的依赖未满足: {step.dependencies}"
                    self.logger.error(error_msg)
                    self.streaming_controller.emit_event('error', error_msg)
                    return {
                        "status": "error",
                        "error": error_msg,
                        "execution_plan": execution_plan.to_dict(),
                        "step_results": step_results
                    }
                
                # 处理输入数据中的变量引用
                # 确保input_data是字符串格式
                if isinstance(step.input_data, dict):
                    input_data_str = json.dumps(step.input_data, ensure_ascii=False)
                else:
                    input_data_str = str(step.input_data)
                processed_input = self._process_step_input(input_data_str, step_outputs)
                
                # 执行步骤
                step_result = await self._execute_step(step, processed_input)
                
                # 更新步骤状态
                self.logger.debug(f"处理步骤结果: {step.step_id}, step_result类型: {type(step_result)}, 内容: {step_result}")
                
                # 检查step_result的类型
                if not isinstance(step_result, dict):
                    self.logger.error(f"步骤结果不是字典类型: {type(step_result)}, 内容: {step_result}")
                    return {
                        "status": "error",
                        "error": f"步骤 {step.step_id} 返回了无效的结果类型: {type(step_result)}",
                        "execution_plan": execution_plan.to_dict(),
                        "step_results": step_results
                    }
                
                if step_result.get("status") == "success":
                    result_content = step_result.get("result", "")
                    self.plan_agent.update_step_status(step.step_id, "completed", str(result_content))
                    # 存储步骤输出 - 存储完整的step_result，保持数据结构一致性
                    step_outputs[step.step_id] = step_result
                    self.logger.info(f"步骤 {step.step_id} 成功，存储输出结构: {type(step_result)}")
                    self.logger.info(f"步骤 {step.step_id} 输出内容: {json.dumps(step_result, ensure_ascii=False, indent=2) if isinstance(step_result, dict) else str(step_result)}")
                    
                    # 发送步骤结果
                    result_data = step_result.get('result', '')
                    output_preview = str(result_data)[:500]
                    result_content = f"执行状态: success\n执行结果: {output_preview}{'...' if len(str(result_data)) > 500 else ''}"
                    
                    self.streaming_controller.emit_event(
                        'step_result',
                        result_content,
                        step_id=step.step_id
                    )
                else:
                    error_content = step_result.get("error", "")
                    self.plan_agent.update_step_status(step.step_id, "failed", str(error_content))
                    # 步骤失败，返回错误
                    error_msg = f"步骤 {step.step_id} 执行失败: {error_content}"
                    self.logger.error(error_msg)
                    self.streaming_controller.emit_event('error', error_msg)
                    return {
                        "status": "error",
                        "error": error_msg,
                        "execution_plan": execution_plan.to_dict(),
                        "step_results": step_results + [step_result]
                    }
                
                step_results.append(step_result)
                self.logger.info(f"步骤 {step.step_id} 执行完成")
            
            # 4. 生成最终答案
            self.streaming_controller.emit_event('final_answer', '正在生成最终答案...')
            final_answer = self._generate_final_answer(user_question, step_results, step_outputs)
            
            # 发送最终答案
            self.logger.info(f"发送给前端的最终答案: {final_answer}")
            self.streaming_controller.emit_event('final_answer', final_answer)
            
            # 5. 将结果添加到短期内存
            self._add_result_to_memory(final_answer, step_results)
            
            # 6. 生成执行摘要
            execution_summary = self._generate_execution_summary(execution_plan, step_results)
            
            # 发送执行摘要
            summary_content = f"总执行时间: {execution_summary.get('total_execution_time', 0):.2f}秒\n"
            summary_content += f"成功步骤数: {execution_summary.get('successful_steps', 0)}/{execution_summary.get('total_steps', 0)}\n"
            summary_content += f"使用的Agent: {', '.join(execution_summary.get('agents_used', []))}\n"
            summary_content += f"处理的数据量: {execution_summary.get('total_data_processed', 0)}字符"
            
            self.streaming_controller.emit_event('summary', summary_content)
            
            self.logger.info(f"{execution_type}完成")
            
            return {
                "status": "success",
                "final_answer": final_answer,
                "execution_plan": execution_plan.to_dict(),
                "step_results": step_results,
                "execution_summary": execution_summary
            }
            
        except Exception as e:
            self.logger.error(f"{execution_type}过程中发生错误: {e}")
            self.streaming_controller.emit_event('error', str(e))
            return {
                "status": "error",
                "error": str(e),
                "execution_plan": None,
                "step_results": []
            }

# =====================================
# Flask API路由定义
# =====================================

@app.route('/')
def index():
    """
    主页面路由 - 返回前端HTML页面
    """
    return send_from_directory('html', 'index.html')

@app.route('/api/chat')
def chat():
    """
    SSE聊天接口 - 核心API
    
    功能：
    1. 接收用户问题（query参数）
    2. 使用全局初始化的MainController处理
    3. 返回SSE流式响应，实时推送执行进度
    4. 支持并发请求，每个请求独立的流式控制器
    
    响应格式：text/event-stream
    事件类型：analysis, plan, step_start, step_result, final_answer, summary, error
    """
    global main_controller
    message = request.args.get('message', '').strip()
    
    if not message:
        return Response(
            'data: {"type": "error", "content": "消息不能为空"}\n\n',
            mimetype='text/event-stream'
        )
    
    def generate():
        # 检查全局controller是否已初始化
        if main_controller is None or not hasattr(main_controller, '_initialized'):
            logger.error("全局MainController未初始化")
            yield 'data: {"type": "error", "content": "系统未初始化，请重启服务"}\n\n'
            return
            
        # 为每个请求创建独立的流式控制器，但复用已初始化的MainController
        streaming_controller = StreamingController()
        streaming_controller.is_processing = True
        
        # 使用线程安全的方式复用全局初始化的MainController
        logger.info("使用已初始化的全局MainController处理请求...")
        
        # 为了线程安全，为每个请求创建一个临时的controller副本
        # 这样可以避免并发请求之间的streaming_controller冲突
        with controller_lock:
            temp_controller = EnhancedMainController(streaming_controller)
            # 复制已初始化的agents，避免重新初始化
            temp_controller.plan_agent = main_controller.plan_agent
            temp_controller.memory_agent = main_controller.memory_agent  
            temp_controller.web_search_agent = main_controller.web_search_agent
            temp_controller.geo_agent = main_controller.geo_agent
            temp_controller.gen_img_agent = main_controller.gen_img_agent
            temp_controller.agent_map = main_controller.agent_map
            temp_controller.memory_pool = main_controller.memory_pool  # 共享内存池
            temp_controller._initialized = True
        
        # 在新线程中处理问题
        def process_question():
            try:
                # 初始化
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                
                async def process_with_global_controller():
                    # 使用临时controller（包含已初始化的agents）
                    logger.info(f"开始处理用户问题: {message}")
                    result = await temp_controller.process_user_question_with_streaming(message)
                    
                    if result['status'] == 'error':
                        streaming_controller.emit_event('error', result.get('error', '处理失败'))
                    
                    logger.info("问题处理完成")
                
                loop.run_until_complete(process_with_global_controller())
                loop.close()
                
            except Exception as e:
                logger.error(f"处理问题时发生异常: {e}")
                streaming_controller.emit_event('error', f'处理异常: {str(e)}')
            finally:
                streaming_controller.is_processing = False
        
        # 启动处理线程
        thread = threading.Thread(target=process_question)
        thread.daemon = True
        thread.start()
        
        # 返回事件流
        yield from streaming_controller.get_events()
    
    return Response(
        generate(),
        mimetype='text/event-stream',
        headers={
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive',
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Cache-Control'
        }
    )

@app.route('/health')
def health():
    """
    健康检查接口
    
    返回服务状态和当前时间戳，用于监控服务可用性
    """
    return {'status': 'ok', 'timestamp': datetime.now().isoformat()}

@app.route('/api/memory/stats')
def memory_stats():
    """
    内存池统计接口
    
    返回短期内存池的统计信息：
    - 总条目数
    - 内存使用情况
    - 最近活动时间等
    """
    try:
        memory_pool = get_memory_pool()
        stats = memory_pool.get_stats()
        return {'status': 'success', 'data': stats}
    except Exception as e:
        return {'status': 'error', 'error': str(e)}, 500

@app.route('/api/memory/context')
def memory_context():
    """
    获取当前内存上下文接口
    
    返回短期内存的上下文信息，用于调试和状态查看：
    - 当前上下文摘要
    - 最近5条记录的详细信息
    """
    try:
        memory_pool = get_memory_pool()
        context = memory_pool.get_context_for_llm()
        recent_items = [item.to_dict() for item in memory_pool.get_recent_context(5)]
        return {
            'status': 'success', 
            'context': context,
            'recent_items': recent_items
        }
    except Exception as e:
        return {'status': 'error', 'error': str(e)}, 500

@app.route('/api/memory/clear', methods=['POST'])
def clear_memory():
    """
    清空内存池接口
    
    POST请求，清空短期内存池中的所有数据，用于重置对话状态
    """
    try:
        memory_pool = get_memory_pool()
        memory_pool.clear()
        return {'status': 'success', 'message': '内存池已清空'}
    except Exception as e:
        return {'status': 'error', 'error': str(e)}, 500

@app.route('/api/proxy-image')
def proxy_image():
    """
    图片代理接口，用于绕过CORS限制
    
    此接口解决前端无法直接访问外部图片URL的CORS问题：
    1. 接收图片URL参数
    2. 服务端代理请求图片资源
    3. 添加适当的CORS头部返回给前端
    4. 支持OSS等云存储服务的签名URL
    """
    import requests
    
    image_url = request.args.get('url')
    if not image_url:
        return 'Missing image URL', 400
    
    # 记录原始URL
    logger.info(f"图片代理请求 - 原始URL: {image_url}")
    
    # 清理URL中的多余字符
    cleaned_url = image_url.strip().strip('`').strip('"').strip("'").strip()
    
    # 移除可能的HTML标签（可能由于前端处理错误导致）
    cleaned_url = re.sub(r'%3C[^%]*%3E', '', cleaned_url)  # 移除URL编码的HTML标签
    cleaned_url = re.sub(r'<[^>]*>', '', cleaned_url)  # 移除普通HTML标签
    
    # 移除可能的Markdown格式
    if cleaned_url.startswith('![') and '](' in cleaned_url:
        # 提取Markdown图片链接中的URL部分
        url_start = cleaned_url.find('](') + 2
        url_end = cleaned_url.rfind(')')
        if url_end > url_start:
            cleaned_url = cleaned_url[url_start:url_end]
    
    # 不进行URL解码，保持原始URL格式以确保签名有效性
    # 对于云存储服务（如OSS），URL签名对字符编码非常敏感
    
    logger.info(f"图片代理请求 - 清理后URL: {cleaned_url}")
    
    # 定期清理过期缓存（每100个请求清理一次）
    if len(image_cache) > 0 and len(image_cache) % 100 == 0:
        cleanup_image_cache()
    
    # 检查缓存
    cache_key = cleaned_url
    if cache_key in image_cache:
        logger.info(f"从缓存返回图片: {cache_key}")
        cached_data = image_cache[cache_key]
        return Response(
            cached_data['content'],
            mimetype=cached_data['content_type'],
            headers={
                'Cache-Control': 'public, max-age=3600',
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'GET, OPTIONS',
                'Access-Control-Allow-Headers': 'Content-Type',
                'X-Cache': 'HIT'
            }
        )
    
    try:
        # 设置请求头，模拟浏览器请求
        headers = {
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'image/webp,image/apng,image/*,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Referer': 'https://dashscope.aliyuncs.com/'
        }
        
        # 请求外部图片，增加重试机制
        max_retries = 3
        for attempt in range(max_retries):
            try:
                # 增加超时时间，使用连接池
                response = requests.get(
                    cleaned_url, 
                    headers=headers, 
                    timeout=(10, 30),  # (连接超时, 读取超时)
                    stream=True,
                    verify=True  # 验证SSL证书
                )
                response.raise_for_status()
                break  # 成功则跳出重试循环
            except requests.exceptions.Timeout as e:
                logger.warning(f"图片请求超时 (尝试 {attempt + 1}/{max_retries}): {e}")
                if attempt == max_retries - 1:
                    return f'Request timeout after {max_retries} attempts', 504
                time.sleep(1)  # 重试前等待1秒
            except requests.exceptions.RequestException as e:
                logger.warning(f"图片请求失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                if attempt == max_retries - 1:
                    raise
                time.sleep(1)
        
        # 获取内容类型
        content_type = response.headers.get('content-type', 'image/jpeg')
        
        # 检查是否为图片类型
        if not content_type.startswith('image/'):
            logger.warning(f"非图片内容类型: {content_type}")
            return f'Invalid content type: {content_type}', 400
        
        # 缓存图片数据（仅缓存小于5MB的图片）
        image_content = response.content
        if len(image_content) < 5 * 1024 * 1024:  # 5MB限制
            # 如果缓存已满，删除最旧的项
            if len(image_cache) >= image_cache_max_size:
                oldest_key = next(iter(image_cache))
                del image_cache[oldest_key]
            
            image_cache[cache_key] = {
                'content': image_content,
                'content_type': content_type,
                'timestamp': time.time()
            }
            logger.info(f"图片已缓存: {cache_key}, 大小: {len(image_content)} bytes")
        
        # 返回图片数据
        return Response(
            image_content,
            mimetype=content_type,
            headers={
                'Cache-Control': 'public, max-age=3600',  # 缓存1小时
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'GET, OPTIONS',
                'Access-Control-Allow-Headers': 'Content-Type',
                'X-Cache': 'MISS'
            }
        )
    except requests.RequestException as e:
        logger.error(f"代理图片失败: {e}")
        return f'Failed to fetch image: {str(e)}', 500
    except Exception as e:
        logger.error(f"处理图片请求时发生未知错误: {e}")
        return f'Internal server error: {str(e)}', 500

# =====================================
# 应用初始化和启动
# =====================================

async def initialize_main_controller():
    """
    初始化全局MainController
    
    在服务启动时执行，避免每次请求都重新初始化Agent：
    1. 创建MainController实例
    2. 初始化所有Agent（异步初始化MCP客户端等）
    3. 设置初始化标志
    """
    global main_controller
    if main_controller is None:
        logger.info("启动时初始化MainController...")
        # 创建临时的StreamingController用于初始化
        temp_controller = StreamingController()
        main_controller = EnhancedMainController(temp_controller)
        
        # 执行初始化
        if await main_controller.initialize():
            main_controller._initialized = True
            logger.info("MainController初始化完成")
        else:
            logger.error("MainController初始化失败")
            main_controller = None

if __name__ == '__main__':
    """
    应用主入口
    
    启动流程：
    1. 初始化全局MainController（包含所有Agent）
    2. 启动Flask Web服务器
    3. 监听8080端口，支持多线程并发
    """
    logger.info("启动Web服务器...")
    
    # 在启动时初始化MainController，避免首次请求延迟
    import asyncio
    asyncio.run(initialize_main_controller())
    
    # 启动Flask应用
    app.run(
        host='0.0.0.0',    # 监听所有网卡
        port=8080,         # 监听端口
        debug=False,       # 生产模式
        threaded=True      # 支持多线程并发请求
    )