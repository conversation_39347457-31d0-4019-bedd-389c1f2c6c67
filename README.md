# Agentic RAG System

一个基于多Agent协作的智能检索增强生成（RAG）系统，采用Orchestrator-workers架构模式，支持复杂任务的自动分解、执行和结果整合。

## 🌟 特性

- **多Agent协作**：5个专门化智能体协同工作
- **智能任务规划**：自动分析用户需求并制定执行计划
- **短期内存管理**：支持上下文保存和智能引用解析
- **实时流式响应**：基于SSE的实时执行进度反馈
- **图像生成与编辑**：集成DashScope图像生成能力
- **多模态支持**：文本、图像、地理信息的统一处理
- **灵活扩展**：基于MCP协议的工具发现和调用

## 🏗️ 系统架构

### 整体架构图

```mermaid
graph TB
    subgraph "用户界面层"
        UI[Web前端界面]
        API[RESTful API]
    end
    
    subgraph "Web服务层"
        WS[Flask Web Server]
        SSE[Server-Sent Events]
        Proxy[图像代理服务]
    end
    
    subgraph "核心控制层"
        MC[MainController<br/>主控制器]
        STM[ShortTermMemoryPool<br/>短期内存池]
    end
    
    subgraph "Agent协作层"
        PA[PlanAgent<br/>规划智能体]
        MA[MemoryAgent<br/>记忆智能体]
        WSA[WebSearchAgent<br/>搜索智能体]
        GA[GeoAgent<br/>地理智能体]
        GIA[GenImgAgent<br/>图像智能体]
    end
    
    subgraph "工具服务层"
        MCP[MCP Client<br/>工具发现]
        Think[Think Tool<br/>推理工具]
        LLM[LLM Service<br/>大语言模型]
    end
    
    subgraph "外部服务"
        DS[DashScope<br/>图像生成]
        DDG[DuckDuckGo<br/>网络搜索]
        AMAP[高德地图MCP<br/>地理/天气服务]
        MEM[本地内存MCP<br/>长期记忆存储]
    end
    
    UI --> API
    API --> WS
    WS --> MC
    WS --> SSE
    WS --> Proxy
    
    MC --> STM
    MC --> PA
    
    PA --> MA
    PA --> WSA
    PA --> GA
    PA --> GIA
    
    MA --> MCP
    WSA --> Think
    GA --> MCP
    GIA --> LLM
    
    WSA --> DDG
    GA --> AMAP
    GIA --> DS
    MCP --> AMAP
    MCP --> MEM
    
    classDef userLayer fill:#e1f5fe
    classDef webLayer fill:#f3e5f5
    classDef controlLayer fill:#e8f5e8
    classDef agentLayer fill:#fff3e0
    classDef toolLayer fill:#fce4ec
    classDef externalLayer fill:#f1f8e9
    
    class UI,API userLayer
    class WS,SSE,Proxy webLayer
    class MC,STM controlLayer
    class PA,MA,WSA,GA,GIA agentLayer
    class MCP,Think,LLM toolLayer
    class DS,DDG,AMAP,MEM externalLayer
```

## 🤖 Agent详细介绍

### 1. PlanAgent - 规划智能体

核心orchestrator，负责任务分析和执行规划。

```mermaid
flowchart TD
    Start[用户问题输入] --> Analyze[LLM任务分析]
    Analyze --> CheckRef{包含引用?}
    CheckRef -->|是| MemFirst[优先调用MemoryAgent]
    CheckRef -->|否| TaskType{任务类型识别}
    MemFirst --> TaskType
    
    TaskType -->|记忆相关| Memory[MemoryAgent<br/>记忆查询/存储]
    TaskType -->|搜索相关| WebSearch[WebSearchAgent<br/>网络搜索]
    TaskType -->|地理相关| Geo[GeoAgent<br/>地理天气]
    TaskType -->|图像相关| GenImg[GenImgAgent<br/>图像生成]
    TaskType -->|复合任务| MultiAgent[多Agent协作]
    
    Memory --> Plan[生成执行计划]
    WebSearch --> Plan
    Geo --> Plan
    GenImg --> Plan
    MultiAgent --> Plan
    
    Plan --> VarRef[设置变量引用<br/>StepID.result]
    VarRef --> Deps[配置依赖关系]
    Deps --> Execute[返回执行计划]
    
    classDef startEnd fill:#4caf50,color:#fff
    classDef process fill:#2196f3,color:#fff
    classDef decision fill:#ff9800,color:#fff
    classDef agent fill:#9c27b0,color:#fff
    
    class Start,Execute startEnd
    class Analyze,Plan,VarRef,Deps process
    class CheckRef,TaskType decision
    class Memory,WebSearch,Geo,GenImg,MultiAgent agent
```

**核心功能：**
- 智能问题分析和任务类型识别
- Agent选择和任务分配
- 变量引用管理（`StepID.result`格式）
- 执行依赖关系设计

### 2. MemoryAgent - 记忆智能体

管理短期和长期内存，支持上下文保存和引用解析。

```mermaid
flowchart TD
    Input[任务输入] --> CheckType{任务类型}
    CheckType -->|查询| SearchFlow[搜索流程]
    CheckType -->|存储| StoreFlow[存储流程]
    
    SearchFlow --> ShortTerm[搜索短期内存]
    ShortTerm --> Found{找到内容?}
    Found -->|是| Return[返回结果]
    Found -->|否| LongTerm[搜索长期内存<br/>MCP工具]
    
    LongTerm --> MCPCall[调用MCP工具服务]
    MCPCall --> Format[格式化结果]
    Format --> Return
    
    StoreFlow --> Validate[验证内容]
    Validate --> Hash[生成内容哈希]
    Hash --> Save[保存到内存]
    Save --> Confirm[确认存储]
    
    classDef input fill:#4caf50,color:#fff
    classDef process fill:#2196f3,color:#fff
    classDef decision fill:#ff9800,color:#fff
    classDef output fill:#9c27b0,color:#fff
    
    class Input input
    class SearchFlow,StoreFlow,ShortTerm,LongTerm,MCPCall,Format,Validate,Hash,Save process
    class CheckType,Found decision
    class Return,Confirm output
```

**核心功能：**
- 双层内存架构（短期+长期）
- 智能引用解析（"上图"、"刚才的"等）
- MCP工具集成（高德地图、本地内存等）
- 内容去重和版本管理

### 3. WebSearchAgent - 网络搜索智能体

基于ReAct模式的智能网络搜索代理。

```mermaid
flowchart TD
    Start[搜索任务] --> React[ReAct循环开始]
    React --> Think[Thought<br/>分析搜索需求]
    Think --> Action[Action<br/>执行搜索]
    Action --> DDG[DuckDuckGo搜索]
    DDG --> WebPage[网页内容抓取]
    WebPage --> Observe[Observation<br/>分析搜索结果]
    
    Observe --> Enough{信息足够?}
    Enough -->|否| Think
    Enough -->|是| Extract[提取关键信息]
    Extract --> Summary[生成搜索摘要]
    Summary --> End[返回最终结果]
    
    classDef startEnd fill:#4caf50,color:#fff
    classDef reactStep fill:#2196f3,color:#fff
    classDef external fill:#ff9800,color:#fff
    classDef decision fill:#9c27b0,color:#fff
    
    class Start,End startEnd
    class Think,Action,Observe,Extract,Summary reactStep
    class DDG,WebPage external
    class Enough decision
```

**核心功能：**
- ReAct（推理-行动-观察）循环
- 智能搜索策略调整
- 网页内容解析和摘要
- 多轮搜索优化

### 4. GeoAgent - 地理智能体

专门处理地理信息、天气查询和位置服务。

```mermaid
flowchart TD
    Input[地理查询请求] --> Parse[解析查询类型]
    Parse --> Weather{天气查询?}
    Parse --> Location{位置查询?}
    Parse --> Route{路径规划?}
    
    Weather -->|是| WeatherAPI[调用天气API]
    Location -->|是| LocationAPI[调用位置API]  
    Route -->|是| RouteAPI[调用路径API]
    
    WeatherAPI --> WeatherProcess[处理天气数据]
    LocationAPI --> LocationProcess[处理位置数据]
    RouteAPI --> RouteProcess[处理路径数据]
    
    WeatherProcess --> Advice[生成建议]
    LocationProcess --> Advice
    RouteProcess --> Advice
    
    Advice --> Format[格式化输出]
    Format --> End[返回结果]
    
    classDef input fill:#4caf50,color:#fff
    classDef decision fill:#ff9800,color:#fff
    classDef api fill:#2196f3,color:#fff
    classDef process fill:#9c27b0,color:#fff
    classDef output fill:#795548,color:#fff
    
    class Input input
    class Weather,Location,Route decision
    class WeatherAPI,LocationAPI,RouteAPI api
    class Parse,WeatherProcess,LocationProcess,RouteProcess,Advice,Format process
    class End output
```

**核心功能：**
- 多类型地理查询支持
- 实时天气数据获取
- 智能出行建议生成
- MCP地理工具集成

### 5. GenImgAgent - 图像智能体

专业的图像生成和编辑智能体。

```mermaid
flowchart TD
    Input[图像任务] --> Analyze[LLM任务分析]
    Analyze --> TaskType{任务类型}
    
    TaskType -->|单张生成| Single[单张图像生成]
    TaskType -->|连续生成| Sequential[连续图像生成] 
    TaskType -->|图像编辑| Edit[图像编辑]
    
    Single --> SingleGen[DashScope生成]
    SingleGen --> SingleResult[返回图像URL]
    
    Sequential --> StoryDecompose[故事分解]
    StoryDecompose --> FirstImage[生成第一张基准图]
    FirstImage --> EditLoop[图像编辑循环]
    EditLoop --> ConsistentImages[保持一致性的图像序列]
    
    Edit --> ExtractURL[提取图像URL]
    ExtractURL --> EditPrompt[生成编辑指令]
    EditPrompt --> ImageEdit[执行图像编辑]
    ImageEdit --> EditResult[返回编辑结果]
    
    classDef input fill:#4caf50,color:#fff
    classDef analysis fill:#2196f3,color:#fff
    classDef decision fill:#ff9800,color:#fff
    classDef generation fill:#9c27b0,color:#fff
    classDef output fill:#795548,color:#fff
    
    class Input input
    class Analyze,StoryDecompose,ExtractURL,EditPrompt analysis
    class TaskType decision
    class Single,Sequential,Edit,SingleGen,FirstImage,EditLoop,ImageEdit generation
    class SingleResult,ConsistentImages,EditResult output
```

**核心功能：**
- 智能任务类型识别
- 连续图像一致性保证
- 基于编辑的序列生成
- 鲁棒的图像URL提取

## 🗄️ 数据流架构

### 主执行流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant WS as Web Server
    participant MC as MainController
    participant PA as PlanAgent
    participant STM as 短期内存
    participant A as 各种Agent
    
    U->>WS: 发送问题
    WS->>STM: 添加用户消息
    WS->>STM: 解析引用
    STM-->>WS: 增强问题上下文
    
    WS->>MC: 处理增强问题
    MC->>PA: 分析问题
    PA-->>MC: 返回执行计划
    
    loop 执行步骤
        MC->>MC: 变量替换
        MC->>A: 执行步骤
        A-->>MC: 返回结果
        MC->>PA: 更新步骤状态
        MC->>WS: 流式返回进展
    end
    
    MC->>MC: 生成最终答案
    MC->>STM: 保存结果
    MC-->>WS: 返回完整结果
    WS-->>U: 流式响应完成
```

### 短期内存系统

```mermaid
graph LR
    subgraph "内存池结构"
        Queue[固定长度队列<br/>最多100项]
        Items[内存项<br/>MemoryItem]
        Index[ID索引<br/>快速查找]
    end
    
    subgraph "内存项结构"
        Role[角色<br/>user/assistant/tool]
        Content[内容<br/>文本/Markdown]
        Artifact[生成物<br/>图片/文件等]
        ID[唯一ID<br/>8位短码]
        Time[时间戳]
    end
    
    subgraph "引用解析"
        Natural[自然语言<br/>上图,刚才的]
        Direct[直接引用<br/>#3,#最新]
        Context[上下文注入<br/>增强问题]
    end
    
    Queue --> Items
    Items --> Role
    Items --> Content  
    Items --> Artifact
    Items --> ID
    Items --> Time
    
    Items --> Natural
    Items --> Direct
    Natural --> Context
    Direct --> Context
    
    classDef structure fill:#e3f2fd
    classDef item fill:#f3e5f5
    classDef reference fill:#e8f5e8
    
    class Queue,Items,Index structure
    class Role,Content,Artifact,ID,Time item
    class Natural,Direct,Context reference
```

## 🛠️ 安装和使用

### 环境要求

- Python >= 3.13
- Flask >= 3.1.2
- 其他依赖见 `requirements.txt` 和 `pyproject.toml`

### 主要依赖

- **Web框架**: Flask 3.1.2+ 和 Flask-CORS 6.0.1+
- **HTTP请求**: requests 2.31.0+
- **搜索引擎**: ddgs 6.1.0+ (DuckDuckGo搜索)
- **HTML解析**: beautifulsoup4 4.12.0+ 和 lxml 4.9.0+
- **异步支持**: aiohttp 3.8.0+
- **WebSocket**: websockets 15.0.1+
- **配置管理**: python-dotenv 1.0.0+

### 安装步骤

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd "agentic rag"
   ```

2. **安装依赖**
   ```bash
   # 使用uv（推荐，支持pyproject.toml）
   uv sync
   
   # 或使用pip安装基础依赖
   pip install -r requirements.txt
   
   # 完整依赖安装（推荐）
   pip install -e .
   ```

3. **环境配置**
   ```bash
   # 复制环境配置文件
   cp .env.example .env
   
   # 编辑配置文件，添加必要的API密钥
   # - DASHSCOPE_API_KEY: 阿里云DashScope API密钥
   # - 其他相关API配置
   ```

4. **启动服务**
   ```bash
   # 使用uv
   uv run python web_server.py
   
   # 或直接运行
   python web_server.py
   
   # 服务将在 http://localhost:8080 启动
   ```

5. **访问应用**
   - 打开浏览器访问 `http://localhost:8080`
   - 开始使用智能助手

### API接口

#### 主要端点

- `POST /api/chat` - 对话接口（同步处理）
- `GET /api/chat/stream` - 流式对话接口（SSE实时响应）
- `GET /api/memory/stats` - 内存状态查询
- `GET /api/memory/context` - 获取内存上下文
- `POST /api/memory/clear` - 清空短期内存
- `GET /api/proxy-image` - 图像代理服务（注意是proxy-image，不是proxy_image）
- `GET /` - 静态文件服务（index.html）

#### 使用示例

```javascript
// 发送对话请求（同步）
fetch('/api/chat', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
    },
    body: JSON.stringify({
        message: '帮我查询北京明天的天气，并生成一张反映天气的图片'
    })
})
.then(response => response.json())
.then(data => {
    console.log('最终结果:', data.final_answer);
    console.log('执行步骤:', data.step_results);
});

// 流式对话（实时响应）
const message = '帮我查询北京明天的天气，并生成一张反映天气的图片';
const eventSource = new EventSource('/api/chat/stream?message=' + encodeURIComponent(message));

eventSource.onmessage = function(event) {
    const data = JSON.parse(event.data);
    if (data.type === 'step_start') {
        console.log('开始执行步骤:', data.step_id);
    } else if (data.type === 'step_complete') {
        console.log('步骤完成:', data.step_id, data.result);
    } else if (data.type === 'final_answer') {
        console.log('最终答案:', data.content);
        eventSource.close();
    }
};

eventSource.onerror = function(event) {
    console.error('SSE连接错误:', event);
    eventSource.close();
};

// 图像代理使用示例
const imageUrl = 'https://example.com/image.jpg';
const proxyUrl = `/api/proxy-image?url=${encodeURIComponent(imageUrl)}`;
// 在HTML中使用: <img src="${proxyUrl}" alt="代理图片" />

// 内存管理示例
// 获取内存统计
fetch('/api/memory/stats')
.then(response => response.json())
.then(data => console.log('内存统计:', data));

// 清空内存
fetch('/api/memory/clear', { method: 'POST' })
.then(response => response.json())
.then(data => console.log('内存清空结果:', data));

## 📁 项目结构

```
agentic rag/
├── agent/                    # 核心Agent实现
│   ├── PlanAgent.py         # 规划智能体（任务分解和执行计划）
│   ├── MemoryAgent.py       # 记忆智能体（上下文管理和引用解析）
│   ├── WebSearchAgent.py    # 网络搜索智能体（ReAct循环搜索）
│   ├── GeoAgent.py          # 地理智能体（地理信息和天气查询）
│   └── GenImgAgent.py       # 图像智能体（图像生成和编辑）
├── html/                    # 前端界面
│   └── index.html          # Web用户界面（支持流式响应）
├── mcpclient/               # MCP客户端集成
│   ├── tool.json           # MCP工具配置
│   └── mcp_client.py       # MCP客户端实现
├── util/                    # 工具模块
│   ├── short_term_memory.py # 短期内存池实现
│   └── llm_client.py       # LLM客户端封装
├── web_server.py           # Flask Web服务器（主入口）
├── requirements.txt        # 基础依赖
├── pyproject.toml         # 完整项目配置
├── .env                   # 环境变量配置
└── README.md              # 项目文档
```

### 核心模块说明

- **web_server.py**: 主控制器，协调整个系统执行流程
- **agent/**: 各种专业化智能体，每个负责特定领域的任务
- **util/short_term_memory.py**: 短期内存管理，支持引用解析和上下文维护
- **mcpclient/**: MCP协议客户端，集成外部工具和服务
- **html/index.html**: 现代化Web界面，支持实时流式响应

## 🔧 配置说明

### MCP工具配置

项目使用MCP（Model Context Protocol）客户端来集成外部工具。在 `mcpclient/tool.json` 中配置外部工具：

```json
{
  "servers": {
    "amap-maps": {
      "type": "streamableHttp",
      "baseUrl": "https://mcp.amap.com/mcp",
      "name": "高德地图服务",
      "description": "提供地理信息、天气查询、POI搜索等服务"
    },
    "local-memory": {
      "type": "local",
      "name": "本地内存服务",
      "description": "长期内存存储和检索服务"
    }
  }
}
```

**主要MCP工具：**
- **高德地图MCP**: 地理信息、天气、POI查询
- **本地内存MCP**: 长期记忆存储和检索
- **Think Tool**: 推理和分析工具（用于WebSearchAgent的ReAct循环）

### LLM配置

在 `.env` 文件中配置LLM服务。项目支持多种LLM提供商：

```env
# OpenAI配置（推荐）
OPENAI_API_KEY=your_openai_api_key
OPENAI_BASE_URL=https://api.openai.com/v1
OPENAI_MODEL=gpt-4o-mini  # 默认模型

# 阿里云DashScope配置（用于图像生成）
DASHSCOPE_API_KEY=your_dashscope_api_key

# 或使用其他兼容OpenAI API的服务
OPENAI_BASE_URL=https://your-custom-endpoint.com/v1

# 可选：调试模式
DEBUG=true
```

**支持的LLM服务：**
- **OpenAI**: GPT-4, GPT-4o, GPT-4o-mini等
- **阿里云DashScope**: 通义千问系列（用于文本分析）+ 通义万相（图像生成）
- **兼容OpenAI API的服务**: 如Azure OpenAI、本地部署的模型等

**模型选择建议：**
- **规划和分析**: GPT-4o-mini（性价比高，响应快）
- **复杂推理**: GPT-4o（准确性更高）
- **图像生成**: 通义万相（支持中文提示词）

## 📊 性能特点

- **高并发处理**: 基于Flask的多线程架构，支持多用户同时访问
- **智能缓存**: 
  - 图片代理缓存（减少重复下载）
  - LLM响应缓存（避免重复计算）
  - 搜索结果缓存（提升响应速度）
- **流式响应**: SSE实时推送执行进度，用户体验更佳
- **内存优化**: 
  - 短期内存池自动管理（队列式FIFO）
  - 智能引用解析（支持ID和自然语言引用）
  - 内存使用监控和统计
- **错误恢复**: 
  - 自动重试机制（网络请求、API调用）
  - 优雅降级策略（Agent执行失败时继续其他步骤）
  - 详细的错误日志和调试信息
- **异步处理**: 
  - 非阻塞的Agent执行
  - 并发的多步骤处理
  - WebSocket支持（为未来扩展准备）
- **模块化设计**：易于扩展和维护

## 🔍 使用案例

### 1. 智能旅行规划
```
用户："我想去北京旅游，帮我查询明天的天气，推荐几个景点，并生成一张北京风景图"

系统执行流程：
1. PlanAgent 分析需求，制定执行计划：
   - 步骤1: 查询北京明天天气 (GeoAgent)
   - 步骤2: 搜索北京热门景点 (WebSearchAgent) 
   - 步骤3: 生成北京风景图 (GenImgAgent)
   - 步骤4: 整合信息生成旅行建议 (MemoryAgent)

2. 执行过程（流式响应）：
   - GeoAgent: "明天北京晴天，气温15-25°C，适合出游"
   - WebSearchAgent: "推荐故宫、天安门、颐和园等景点"
   - GenImgAgent: 生成北京天安门广场的风景图
   - MemoryAgent: 整合所有信息，提供详细的旅行建议

3. 最终输出：包含天气信息、景点推荐、风景图片的完整旅行规划
```

### 2. 学术研究辅助
```
用户："帮我搜索人工智能在医疗领域的最新应用，并生成一个总结图表"

系统执行流程：
1. PlanAgent 制定研究计划：
   - 步骤1: 搜索AI医疗应用最新进展 (WebSearchAgent)
   - 步骤2: 分析和整理搜索结果 (MemoryAgent)
   - 步骤3: 生成可视化总结图表 (GenImgAgent)

2. 执行过程：
   - WebSearchAgent: 使用ReAct循环深度搜索，找到最新的AI医疗应用案例
   - MemoryAgent: 从短期内存中提取关键信息，进行结构化分析
   - GenImgAgent: 根据分析结果生成信息图表或流程图

3. 最终输出：结构化的研究总结 + 可视化图表
```

### 3. 多轮对话与引用
```
用户："生成一张猫的图片"
系统：[生成图片，ID: img_001]

用户："把刚才的图片改成狗"
系统执行流程：
1. PlanAgent 识别引用关系，解析"刚才的图片"为 img_001
2. GenImgAgent 基于原图片进行图像编辑，将猫改为狗
3. 返回编辑后的新图片

引用支持：
- 精确ID引用: "修改 {{img_001}}"
- 自然语言引用: "刚才的图片"、"上一张图"、"第一个结果"
```

## ❓ 常见问题

### Q: 启动服务时提示端口被占用怎么办？
A: 默认端口是8080，可以通过修改 `web_server.py` 中的端口号，或者使用 `lsof -i :8080` 查看占用进程并终止。

### Q: 图片代理功能不工作？
A: 检查以下几点：
1. 确保目标图片URL可访问
2. 检查网络连接
3. 查看控制台错误日志
4. 确认 `/api/proxy-image` 端点正常

### Q: LLM调用失败？
A: 检查 `.env` 文件配置：
1. `OPENAI_API_KEY` 是否正确
2. `OPENAI_BASE_URL` 是否可访问
3. 网络是否正常
4. API配额是否充足

### Q: 如何添加新的Agent？
A: 参考现有Agent实现：
1. 在 `agent/` 目录创建新的Agent类
2. 实现 `execute()` 方法
3. 在 `PlanAgent.py` 中注册新Agent
4. 更新提示词模板

### Q: 内存使用过高怎么办？
A: 可以调整短期内存池大小：
1. 修改 `short_term_memory.py` 中的 `max_size` 参数
2. 定期调用 `/api/memory/clear` 清空内存
3. 优化图片缓存策略

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

### 代码规范

- 遵循PEP 8代码风格
- 添加适当的类型注解和文档字符串
- 编写单元测试和集成测试
- 确保所有测试通过
- 更新相关文档

### 开发建议

- 新增Agent时，参考现有Agent的实现模式
- 修改核心逻辑前，先添加相应的测试用例
- 提交前使用 `python -m flake8` 检查代码风格
- 大型功能建议先创建Issue讨论设计方案

## 📄 许可证

本项目采用 MIT 许可证 - 详见 [LICENSE](LICENSE) 文件

## 🙏 致谢

- [DashScope](https://dashscope.aliyuncs.com/) - 图像生成服务
- [DuckDuckGo](https://duckduckgo.com/) - 搜索服务
- [Flask](https://flask.palletsprojects.com/) - Web框架
- [千问大模型](https://tongyi.aliyun.com/) - 语言模型服务

---

**注意**: 这是一个实验性项目，持续开发中。欢迎提交Issue和贡献代码！
