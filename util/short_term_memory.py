"""
短期内存池系统
用于保存和检索最近的交互信息，支持结构化存储和智能引用
"""

import json
import uuid
from datetime import datetime
from typing import List, Dict, Any, Optional, Union
from collections import deque
import logging
import re


class MemoryItem:
    """内存项数据结构"""
    
    def __init__(self, role: str, content: str, artifact: Optional[Dict[str, Any]] = None, 
                 item_id: Optional[str] = None, timestamp: Optional[str] = None):
        self.role = role  # "user" | "assistant" | "tool"
        self.content = content
        self.artifact = artifact or {}
        self.item_id = item_id or str(uuid.uuid4())[:8]
        self.timestamp = timestamp or datetime.now().isoformat()
        
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "role": self.role,
            "content": self.content,
            "artifact": self.artifact,
            "item_id": self.item_id,
            "timestamp": self.timestamp
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'MemoryItem':
        """从字典创建实例"""
        return cls(
            role=data["role"],
            content=data["content"],
            artifact=data.get("artifact", {}),
            item_id=data.get("item_id"),
            timestamp=data.get("timestamp")
        )
    
    def has_artifact_type(self, artifact_type: str) -> bool:
        """检查是否包含指定类型的生成物"""
        return self.artifact.get("type") == artifact_type
    
    def get_artifact_id(self) -> Optional[str]:
        """获取生成物ID"""
        return self.artifact.get("id")


class ShortTermMemoryPool:
    """短期内存池"""
    
    def __init__(self, max_size: int = 10):
        self.max_size = max_size
        self.memory_queue = deque(maxlen=max_size)  # 固定长度队列
        self.artifact_counter = 0  # 生成物计数器
        self.logger = logging.getLogger(__name__)
        
    def add_user_message(self, content: str) -> str:
        """添加用户消息"""
        item = MemoryItem(role="user", content=content)
        self.memory_queue.append(item)
        self.logger.info(f"添加用户消息到短期内存: {content[:50]}...")
        return item.item_id
    
    def add_assistant_message(self, content: str, artifact: Optional[Dict[str, Any]] = None) -> str:
        """添加助手回复，可包含生成物"""
        if artifact:
            # 为生成物分配短ID
            self.artifact_counter += 1
            artifact_id = f"#{self.artifact_counter}"
            artifact["id"] = artifact_id
            
            # 更新消息内容，包含生成物ID
            if artifact.get("type") == "image":
                content += f" (图片ID: {artifact_id})"
            elif artifact.get("type") == "text":
                content += f" (文档ID: {artifact_id})"
            elif artifact.get("type") == "audio":
                content += f" (音频ID: {artifact_id})"
            
        item = MemoryItem(role="assistant", content=content, artifact=artifact)
        self.memory_queue.append(item)
        self.logger.info(f"添加助手回复到短期内存: {content[:50]}...")
        return item.item_id
    
    def add_tool_result(self, content: str, tool_name: str) -> str:
        """添加工具执行结果"""
        artifact = {"type": "tool_result", "tool_name": tool_name}
        item = MemoryItem(role="tool", content=content, artifact=artifact)
        self.memory_queue.append(item)
        self.logger.info(f"添加工具结果到短期内存: {tool_name}")
        return item.item_id
    
    def find_by_artifact_id(self, artifact_id: str) -> Optional[MemoryItem]:
        """根据生成物ID查找"""
        for item in reversed(self.memory_queue):  # 从最新开始查找
            if item.get_artifact_id() == artifact_id:
                return item
        return None
    
    def find_latest_by_artifact_type(self, artifact_type: str) -> Optional[MemoryItem]:
        """查找最新的指定类型生成物"""
        for item in reversed(self.memory_queue):
            if item.has_artifact_type(artifact_type):
                return item
        return None
    
    def find_recent_artifacts(self, limit: int = 5) -> List[MemoryItem]:
        """获取最近的生成物（有artifact的项）"""
        artifacts = []
        for item in reversed(self.memory_queue):
            if item.artifact and item.artifact.get("type"):
                artifacts.append(item)
                if len(artifacts) >= limit:
                    break
        return artifacts
    
    def get_recent_context(self, limit: int = 10) -> List[MemoryItem]:
        """获取最近的上下文"""
        return list(reversed(list(self.memory_queue)[-limit:]))
    
    def resolve_reference(self, user_input: str) -> Optional[Dict[str, Any]]:
        """
        解析用户输入中的引用
        支持：#1, #2 等精确ID引用
        支持：上张图、刚刚那个文档等自然语言引用
        """
        # 1. 检查精确ID引用 (#1, #2等)
        id_pattern = r'#(\d+)'
        id_matches = re.findall(id_pattern, user_input)
        if id_matches:
            artifact_id = f"#{id_matches[0]}"
            item = self.find_by_artifact_id(artifact_id)
            if item:
                return {
                    "type": "exact_id",
                    "artifact_id": artifact_id,
                    "item": item,
                    "resolved_text": f"引用生成物 {artifact_id}"
                }
        
        # 2. 检查自然语言引用
        natural_refs = {
            "上张图": "image",
            "最后一张图": "image", 
            "刚刚的图": "image",
            "那张图": "image",
            "上个文档": "text",
            "刚刚的文档": "text",
            "最后的音频": "audio",
            "上个音频": "audio"
        }
        
        for phrase, artifact_type in natural_refs.items():
            if phrase in user_input:
                item = self.find_latest_by_artifact_type(artifact_type)
                if item:
                    return {
                        "type": "natural_language",
                        "phrase": phrase,
                        "artifact_type": artifact_type,
                        "item": item,
                        "resolved_text": f"引用最近的{artifact_type}类型生成物 {item.get_artifact_id()}"
                    }
        
        return None
    
    def get_context_for_llm(self, limit: int = 5) -> str:
        """为LLM准备上下文描述"""
        recent_artifacts = self.find_recent_artifacts(limit)
        if not recent_artifacts:
            return "暂无最近的生成物。"
        
        context_lines = [f"最近 {len(recent_artifacts)} 个生成物："]
        for i, item in enumerate(recent_artifacts):
            artifact = item.artifact
            artifact_id = artifact.get("id", "未知")
            artifact_type = artifact.get("type", "未知")
            
            # 简化内容描述
            content_preview = item.content[:50] + "..." if len(item.content) > 50 else item.content
            context_lines.append(f"{artifact_id} [{artifact_type}] {content_preview}")
        
        return "\n".join(context_lines)
    
    def clear(self):
        """清空内存池"""
        self.memory_queue.clear()
        self.artifact_counter = 0
        self.logger.info("短期内存池已清空")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取内存池统计信息"""
        total_items = len(self.memory_queue)
        artifact_counts = {}
        
        for item in self.memory_queue:
            if item.artifact and item.artifact.get("type"):
                artifact_type = item.artifact["type"]
                artifact_counts[artifact_type] = artifact_counts.get(artifact_type, 0) + 1
        
        return {
            "total_items": total_items,
            "max_size": self.max_size,
            "artifact_counts": artifact_counts,
            "next_artifact_id": f"#{self.artifact_counter + 1}"
        }
    
    def to_dict(self) -> Dict[str, Any]:
        """序列化为字典"""
        return {
            "max_size": self.max_size,
            "artifact_counter": self.artifact_counter,
            "memory_queue": [item.to_dict() for item in self.memory_queue]
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ShortTermMemoryPool':
        """从字典反序列化"""
        pool = cls(max_size=data["max_size"])
        pool.artifact_counter = data["artifact_counter"]
        
        for item_data in data["memory_queue"]:
            item = MemoryItem.from_dict(item_data)
            pool.memory_queue.append(item)
        
        return pool


# 全局短期内存池实例
global_memory_pool = ShortTermMemoryPool()


def get_memory_pool() -> ShortTermMemoryPool:
    """获取全局内存池实例"""
    return global_memory_pool


# 使用示例
if __name__ == "__main__":
    # 创建内存池
    memory = ShortTermMemoryPool()
    
    # 添加用户消息
    memory.add_user_message("请生成一张猫咪图片")
    
    # 添加助手回复（包含图片生成物）
    image_artifact = {
        "type": "image",
        "url": "https://example.com/cat.jpg",
        "description": "一只可爱的小猫"
    }
    memory.add_assistant_message("好的，我为你生成了一张猫咪图片", image_artifact)
    
    # 添加用户消息
    memory.add_user_message("改成黑猫")
    
    # 测试引用解析
    ref_result = memory.resolve_reference("把上张图改成黑猫")
    print(f"引用解析结果: {ref_result}")
    
    # 获取上下文
    context = memory.get_context_for_llm()
    print(f"LLM上下文: {context}")
    
    # 获取统计信息
    stats = memory.get_stats()
    print(f"内存池统计: {stats}")


