import requests
import json
import os
import time
from typing import Dict, Any, Optional
import logging


class ImageGenerator:
    """
    文生图工具类 - 基于阿里云DashScope API
    """
    
    def __init__(self, api_key: Optional[str] = None):
        """
        初始化文生图工具
        
        Args:
            api_key: DashScope API密钥，如果不提供则从环境变量DASHSCOPE_API_KEY获取
        """
        self.api_key = api_key or os.getenv('DASHSCOPE_API_KEY')
        if not self.api_key:
            raise ValueError("请提供DASHSCOPE_API_KEY环境变量或直接传入api_key参数")
        
        self.base_url = "https://dashscope.aliyuncs.com/api/v1/services/aigc/text2image/image-synthesis"
        self.logger = logging.getLogger(__name__)
        
    def generate_image(self, 
                      prompt: str,
                      model: str = "qwen-image",
                      size: str = "1328*1328",
                      n: int = 1,
                      prompt_extend: bool = True,
                      watermark: bool = False,
                      async_mode: bool = True) -> Dict[str, Any]:
        """
        生成图片
        
        Args:
            prompt: 文本描述
            model: 模型名称，默认为qwen-image
            size: 图片尺寸，默认为1328*1328
            n: 生成图片数量，默认为1
            prompt_extend: 是否扩展提示词，默认为True
            watermark: 是否添加水印，默认为True
            async_mode: 是否使用异步模式，默认为True
            
        Returns:
            API响应结果
        """
        try:
            # 构建请求头
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            # 如果使用异步模式，添加异步头
            if async_mode:
                headers["X-DashScope-Async"] = "enable"
            
            # 构建请求体
            payload = {
                "model": model,
                "input": {
                    "prompt": prompt
                },
                "parameters": {
                    "size": size,
                    "n": n,
                    "prompt_extend": prompt_extend,
                    "watermark": watermark
                }
            }
            
            self.logger.info(f"开始生成图片，提示词: {prompt[:50]}...")
            
            # 发送请求
            response = requests.post(
                self.base_url,
                headers=headers,
                json=payload,
                timeout=30
            )
            
            # 检查响应状态
            response.raise_for_status()
            
            result = response.json()
            
            # 如果是异步模式，需要轮询获取结果
            if async_mode and "output" in result and "task_id" in result["output"]:
                task_id = result["output"]["task_id"]
                self.logger.info(f"异步任务已提交，任务ID: {task_id}")
                return self._poll_async_result(task_id)
            
            self.logger.info("图片生成完成")
            return {
                "status": "success",
                "result": result
            }
            
        except requests.exceptions.RequestException as e:
            self.logger.error(f"请求失败: {str(e)}")
            return {
                "status": "error",
                "error": f"请求失败: {str(e)}"
            }
        except Exception as e:
            self.logger.error(f"生成图片失败: {str(e)}")
            return {
                "status": "error",
                "error": f"生成图片失败: {str(e)}"
            }
    
    def _poll_async_result(self, task_id: str, max_wait_time: int = 300, poll_interval: int = 5) -> Dict[str, Any]:
        """
        轮询异步任务结果
        
        Args:
            task_id: 任务ID
            max_wait_time: 最大等待时间（秒），默认300秒
            poll_interval: 轮询间隔（秒），默认5秒
            
        Returns:
            任务结果
        """
        start_time = time.time()
        
        while time.time() - start_time < max_wait_time:
            try:
                # 查询任务状态
                status_url = f"https://dashscope.aliyuncs.com/api/v1/tasks/{task_id}"
                headers = {
                    "Authorization": f"Bearer {self.api_key}"
                }
                
                response = requests.get(status_url, headers=headers, timeout=10)
                response.raise_for_status()
                
                result = response.json()
                task_status = result.get("output", {}).get("task_status", "")
                
                if task_status == "SUCCEEDED":
                    self.logger.info(f"异步任务完成: {task_id}")
                    return {
                        "status": "success",
                        "result": result
                    }
                elif task_status == "FAILED":
                    error_msg = result.get("output", {}).get("message", "任务失败")
                    self.logger.error(f"异步任务失败: {task_id}, 错误: {error_msg}")
                    return {
                        "status": "error",
                        "error": f"任务失败: {error_msg}"
                    }
                elif task_status in ["PENDING", "RUNNING"]:
                    self.logger.info(f"任务进行中: {task_id}, 状态: {task_status}")
                    time.sleep(poll_interval)
                    continue
                else:
                    self.logger.warning(f"未知任务状态: {task_status}")
                    time.sleep(poll_interval)
                    continue
                    
            except Exception as e:
                self.logger.error(f"查询任务状态失败: {str(e)}")
                time.sleep(poll_interval)
                continue
        
        # 超时
        self.logger.error(f"任务超时: {task_id}")
        return {
            "status": "error",
            "error": "任务超时"
        }
    
    def generate_image_sync(self, prompt: str, **kwargs) -> Dict[str, Any]:
        """
        同步生成图片（不使用异步模式）
        
        Args:
            prompt: 文本描述
            **kwargs: 其他参数
            
        Returns:
            API响应结果
        """
        kwargs['async_mode'] = False
        return self.generate_image(prompt, **kwargs)
    
    def edit_image(self, 
                   image_url: str,
                   edit_prompt: str,
                   model: str = "qwen-image-edit",
                   negative_prompt: str = "",
                   watermark: bool = False) -> Dict[str, Any]:
        """
        编辑图片
        
        Args:
            image_url: 原始图片URL
            edit_prompt: 编辑指令文本
            model: 模型名称，默认为qwen-image-edit
            negative_prompt: 负面提示词，默认为空
            watermark: 是否添加水印，默认为False
            
        Returns:
            API响应结果
        """
        try:
            # 构建请求头
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            # 构建请求体
            payload = {
                "model": model,
                "input": {
                    "messages": [
                        {
                            "role": "user",
                            "content": [
                                {
                                    "image": image_url
                                },
                                {
                                    "text": edit_prompt
                                }
                            ]
                        }
                    ]
                },
                "parameters": {
                    "negative_prompt": negative_prompt,
                    "watermark": watermark
                }
            }
            
            self.logger.info(f"开始编辑图片，编辑指令: {edit_prompt[:50]}...")
            
            # 发送请求到图像编辑API
            edit_url = "https://dashscope.aliyuncs.com/api/v1/services/aigc/multimodal-generation/generation"
            response = requests.post(
                edit_url,
                headers=headers,
                json=payload,
                timeout=60  # 图像编辑可能需要更长时间
            )
            
            # 检查响应状态
            response.raise_for_status()
            
            result = response.json()
            
            self.logger.info("图片编辑完成")
            return {
                "status": "success",
                "result": result
            }
            
        except requests.exceptions.RequestException as e:
            self.logger.error(f"图片编辑请求失败: {str(e)}")
            return {
                "status": "error",
                "error": f"图片编辑请求失败: {str(e)}"
            }
        except Exception as e:
            self.logger.error(f"图片编辑失败: {str(e)}")
            return {
                "status": "error",
                "error": f"图片编辑失败: {str(e)}"
            }
    
    def extract_image_urls(self, result: Dict[str, Any]) -> list:
        """
        从API结果中提取图片URL
        
        Args:
            result: API响应结果
            
        Returns:
            图片URL列表
        """
        urls = []
        try:
            if result.get("status") == "success":
                api_result = result.get("result", {})
                output = api_result.get("output", {})
                
                # 处理不同的响应格式
                if "results" in output:
                    for item in output["results"]:
                        if "url" in item:
                            urls.append(item["url"])
                elif "url" in output:
                    urls.append(output["url"])
                # 处理图像编辑的响应格式
                elif "choices" in output:
                    for choice in output["choices"]:
                        message = choice.get("message", {})
                        content = message.get("content", [])
                        for item in content:
                            if isinstance(item, dict) and "image" in item:
                                urls.append(item["image"])
                    
        except Exception as e:
            self.logger.error(f"提取图片URL失败: {str(e)}")
            
        return urls


# 使用示例
if __name__ == "__main__":
    # 设置日志
    logging.basicConfig(level=logging.INFO)
    
    # 创建图片生成器
    generator = ImageGenerator()
    
    # 示例1: 生成图片
    print("=== 图片生成示例 ===")
    prompt = "一副典雅庄重的对联悬挂于厅堂之中，房间是个安静古典的中式布置，桌子上放着一些青花瓷，对联上左书\"义本生知人机同道善思新\"，右书\"通云赋智乾坤启数高志远\"， 横批\"智启通义\"，字体飘逸，中间挂在一着一副中国风的画作，内容是岳阳楼。"
    
    result = generator.generate_image(prompt)
    
    if result["status"] == "success":
        urls = generator.extract_image_urls(result)
        print(f"生成成功，图片URL: {urls}")
    else:
        print(f"生成失败: {result['error']}")
    
    # 示例2: 编辑图片
    print("\n=== 图片编辑示例 ===")
    image_url = "https://dashscope.oss-cn-beijing.aliyuncs.com/images/dog_and_girl.jpeg"
    edit_prompt = "将图中的人物改为站立姿势，弯腰握住狗的前爪"
    
    edit_result = generator.edit_image(image_url, edit_prompt)
    
    if edit_result["status"] == "success":
        edit_urls = generator.extract_image_urls(edit_result)
        print(f"编辑成功，编辑后图片URL: {edit_urls}")
    else:
        print(f"编辑失败: {edit_result['error']}")