import requests
import json
import os
import re
from typing import List, Dict, Any, Optional
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

class OpenRouterLLM:
    """OpenRouter LLM调用类"""
    
    def __init__(self):
        self.api_key = os.getenv('OPENROUTER_API_KEY')
        self.site_url = os.getenv('SITE_URL', '')
        self.site_name = os.getenv('SITE_NAME', '')
        self.base_url = "https://openrouter.ai/api/v1/chat/completions"
        
        if not self.api_key:
            raise ValueError("OPENROUTER_API_KEY not found in environment variables")
    
    def call_llm(self, 
                 messages: List[Dict[str, str]], 
                 model: str = "openai/gpt-4o",
                 temperature: float = 0.7,
                 max_tokens: Optional[int] = None,
                 **kwargs) -> Dict[str, Any]:
        """
        调用OpenRouter LLM API
        
        Args:
            messages: 消息列表，格式为 [{"role": "user", "content": "message"}]
            model: 模型名称，默认为 "openai/gpt-4o"
            temperature: 温度参数，控制输出随机性
            max_tokens: 最大token数
            **kwargs: 其他API参数
            
        Returns:
            API响应的字典格式
        """
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        # 添加可选的网站信息
        if self.site_url:
            headers["HTTP-Referer"] = self.site_url
        if self.site_name:
            headers["X-Title"] = self.site_name
        
        # 构建请求数据
        data = {
            "model": model,
            "messages": messages,
            "temperature": temperature
        }
        
        if max_tokens:
            data["max_tokens"] = max_tokens
            
        # 添加其他参数
        data.update(kwargs)
        
        try:
            response = requests.post(
                url=self.base_url,
                headers=headers,
                data=json.dumps(data)
            )
            response.raise_for_status()
            return response.json()
            
        except requests.exceptions.RequestException as e:
            raise Exception(f"API调用失败: {e}")
        except json.JSONDecodeError as e:
            raise Exception(f"响应解析失败: {e}")
    
    def simple_chat(self, user_message: str, model: str = "openai/gpt-4o") -> str:
        """
        简单的聊天接口
        
        Args:
            user_message: 用户消息
            model: 模型名称
            
        Returns:
            LLM的回复内容
        """
        messages = [{"role": "user", "content": user_message}]
        response = self.call_llm(messages, model)
        return response['choices'][0]['message']['content']


# 便捷函数
def create_llm_client() -> OpenRouterLLM:
    """创建LLM客户端实例"""
    return OpenRouterLLM()


def quick_chat(message: str, model: str = "openai/gpt-4o") -> str:
    """快速聊天函数"""
    client = create_llm_client()
    return client.simple_chat(message, model)


def llm_call(prompt: str, system_prompt: str = "", model="openai/gpt-4o") -> str:
    """
    Calls the model with the given prompt and returns the response.

    Args:
        prompt (str): The user prompt to send to the model.
        system_prompt (str, optional): The system prompt to send to the model. Defaults to "".
        model (str, optional): The model to use for the call. Defaults to "openai/gpt-4o".

    Returns:
        str: The response from the language model.
    """
    messages = [{"role": "user", "content": prompt}]
    if system_prompt:
        messages.insert(0, {"role": "system", "content": system_prompt})

    response = create_llm_client().call_llm(messages, model)
    return response['choices'][0]['message']['content']


def extract_xml(text: str, tag: str) -> str:
    """
    Extracts the content of the specified XML tag from the given text. Used for parsing structured responses

    Args:
        text (str): The text containing the XML.
        tag (str): The XML tag to extract content from.

    Returns:
        str: The content of the specified XML tag, or an empty string if the tag is not found.
    """
    match = re.search(f'<{tag}>(.*?)</{tag}>', text, re.DOTALL)
    return match.group(1) if match else ""




# 示例用法
if __name__ == "__main__":
    # 创建客户端
    llm = create_llm_client()
    
    # 示例1: 简单聊天
    try:
        response = llm.simple_chat("What is the meaning of life?")
        print("简单聊天回复:")
        print(response)
        print("\n" + "="*50 + "\n")
    except Exception as e:
        print(f"错误: {e}")
    
    # 示例2: 完整API调用
    try:
        messages = [
            {"role": "system", "content": "You are a helpful assistant."},
            {"role": "user", "content": "Explain quantum computing in simple terms."}
        ]
        
        full_response = llm.call_llm(
            messages=messages,
            model="openai/gpt-4o",
            temperature=0.5,
            max_tokens=200
        )
        
        print("完整API调用回复:")
        print(full_response['choices'][0]['message']['content'])
        print(f"\n使用的模型: {full_response.get('model', 'Unknown')}")
        print(f"Token使用情况: {full_response.get('usage', {})}")
        
    except Exception as e:
        print(f"错误: {e}")
    
    # 示例3: 快速聊天函数
    try:
        quick_response = quick_chat("Tell me a joke")
        print("\n" + "="*50 + "\n")
        print("快速聊天回复:")
        print(quick_response)
    except Exception as e:
        print(f"错误: {e}")