import json
import os
import sys
from typing import Dict, List, Any, Optional
from datetime import datetime
import logging
import hashlib

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from mcpclient.McpClient import <PERSON>cp<PERSON><PERSON>
from mcpclient.ThinkTool import think_tool, get_think_tool_schema
from util.llmcallqianwen import create_llm_client
from util.short_term_memory import ShortTermMemoryPool, get_memory_pool


class MemoryItem:
    """记忆项类"""
    def __init__(self, key: str, content: str, metadata: Optional[Dict[str, Any]] = None):
        self.key = key
        self.content = content
        self.metadata = metadata or {}
        self.created_at = datetime.now().isoformat()
        self.updated_at = self.created_at
        
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "key": self.key,
            "content": self.content,
            "metadata": self.metadata,
            "created_at": self.created_at,
            "updated_at": self.updated_at
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'MemoryItem':
        """从字典创建记忆项"""
        item = cls(data["key"], data["content"], data.get("metadata", {}))
        item.created_at = data.get("created_at", item.created_at)
        item.updated_at = data.get("updated_at", item.updated_at)
        return item
    
    def __str__(self) -> str:
        return f"MemoryItem({self.key}: {self.content[:50]}...)"


class MemoryAgent:
    """记忆Agent - 用于搜索并存储记忆的agent
    可以接受PlanAgent分发的任务，具备理解输入、调用工具的能力
    """
    
    def __init__(self, config_file: str = "mcpclient/tool.json"):
        """
        初始化记忆Agent
        
        Args:
            config_file: MCP配置文件路径
        """
        self.mcp_client = McpClient(config_file)
        self.llm_client = create_llm_client()  # LLM客户端
        self.logger = logging.getLogger(__name__)
        self.memory_server_id = None  # 记忆服务器ID
        self.is_initialized = False
        self.memory_tools = []  # 存储记忆相关工具的schema
        self.short_term_memory = get_memory_pool()  # 短期内存池
        
    async def initialize(self) -> bool:
        """
        初始化记忆Agent，发现MCP记忆服务并收集工具schema
        
        Returns:
            是否初始化成功
        """
        try:
            # 发现MCP工具
            success = await self.mcp_client.discover_tools()
            if not success:
                self.logger.error("MCP工具发现失败")
                return False
            
            # 查找记忆相关工具并收集schema
            memory_tools = []
            for tool_key, tool in self.mcp_client.tools.items():
                if any(keyword in tool.name.lower() for keyword in ['save_memory', 'get_all_memories', 'search_memories', 'clear_all_memories']):
                    memory_tools.append((tool_key, tool))
                    if not self.memory_server_id:
                        self.memory_server_id = tool_key.split(':')[0]
                    
                    # 收集工具schema用于LLM调用（符合OpenAI function calling格式）
                    self.memory_tools.append({
                        "type": "function",
                        "function": {
                            "name": tool.name,
                            "description": tool.description,
                            "parameters": tool.input_schema if hasattr(tool, 'input_schema') else {}
                        }
                    })
            
            if not memory_tools:
                self.logger.warning("未找到记忆相关工具")
                return False
            
            self.logger.info(f"发现 {len(memory_tools)} 个记忆工具:")
            for tool_key, tool in memory_tools:
                self.logger.info(f"  - {tool}")
            
            self.is_initialized = True
            return True
            
        except Exception as e:
            self.logger.error(f"记忆Agent初始化失败: {str(e)}")
            return False
    

    
    async def process_task(self, task_input: str) -> Dict[str, Any]:
        """
        处理PlanAgent分发的记忆相关任务
        完整流程：先查短期内存 -> 再查长期内存 -> 返回结果
        
        Args:
            task_input: 任务输入描述
            
        Returns:
            处理结果，格式类似WebSearchAgent
        """
        if not self.is_initialized:
            await self.initialize()
        
        try:
            self.logger.info(f"开始处理记忆任务: {task_input}")
            
            # 1. 先尝试从短期内存查询
            short_term_result = await self._search_short_term_memory(task_input)
            
            if short_term_result.get("found"):
                # 如果在短期内存中找到了相关内容，直接返回
                self.logger.info(f"在短期内存中找到相关内容: {short_term_result['description']}")
                return {
                    "status": "success",
                    "task_input": task_input,
                    "result": short_term_result["result"],
                    "source": "short_term_memory",
                    "artifact_info": short_term_result.get("artifact_info"),
                    "timestamp": datetime.now().isoformat()
                }
            
            # 2. 短期内存中没找到，查询长期内存
            self.logger.info("短期内存中未找到相关内容，查询长期内存...")
            tool_call_result = await self._understand_and_call_tool(task_input)
            
            # 提取可读的最终答案
            final_answer_parts = []
            if tool_call_result.get("status") == "success":
                tool_calls = tool_call_result.get("tool_calls", [])
                for tool_call in tool_calls:
                    if tool_call.get("tool") != "think":
                        result = tool_call.get("result", {})
                        if result.get("status") == "success":
                            content = result.get("result", {}).get("content", [])
                            for item in content:
                                if item.get("type") == "text":
                                    final_answer_parts.append(item.get("text", ""))
            
            # 生成最终的可读答案
            if final_answer_parts:
                final_answer = "\n".join(final_answer_parts)
            else:
                final_answer = "未找到相关记忆"
            
            self.logger.info(f"记忆任务处理完成: {task_input}")
            
            # 返回类似WebSearchAgent的格式
            return {
                "status": tool_call_result.get("status", "success"),
                "task_input": task_input,
                "result": final_answer,
                "source": "long_term_memory",
                "tool_calls": tool_call_result.get("tool_calls", []),
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"处理记忆任务异常: {str(e)}")
            return {
                "status": "error",
                "task_input": task_input,
                "error": f"任务处理异常: {str(e)}",
                "timestamp": datetime.now().isoformat()
            }
    
    async def _search_short_term_memory(self, task_input: str) -> Dict[str, Any]:
        """
        在短期内存中搜索相关内容
        
        Args:
            task_input: 任务输入描述
            
        Returns:
            搜索结果，包含是否找到和相关信息
        """
        try:
            self.logger.info(f"开始在短期内存中搜索: {task_input}")
            
            # 1. 检查是否有直接引用（如"上图"、"刚刚的图片"等）
            reference_result = self.short_term_memory.resolve_reference(task_input)
            if reference_result:
                item = reference_result["item"]
                artifact = item.artifact
                
                # 构建详细的引用信息
                result_text = f"找到引用内容: {reference_result['resolved_text']}\n"
                result_text += f"内容: {item.content}\n"
                
                if artifact.get("url"):
                    result_text += f"资源地址: {artifact['url']}\n"
                if artifact.get("description"):
                    result_text += f"描述: {artifact['description']}\n"
                
                return {
                    "found": True,
                    "result": result_text,
                    "description": reference_result["resolved_text"],
                    "artifact_info": {
                        "type": artifact.get("type"),
                        "id": artifact.get("id"),
                        "url": artifact.get("url"),
                        "description": artifact.get("description"),
                        "original_content": item.content
                    },
                    "reference_type": reference_result["type"]
                }
            
            # 2. 检查是否询问关于图片、文档等生成物的问题
            if any(keyword in task_input.lower() for keyword in ["图", "image", "图片", "图像", "picture"]):
                latest_image = self.short_term_memory.find_latest_by_artifact_type("image")
                if latest_image:
                    artifact = latest_image.artifact
                    result_text = f"找到最新的图片:\n"
                    result_text += f"内容: {latest_image.content}\n"
                    if artifact.get("url"):
                        result_text += f"图片地址: {artifact['url']}\n"
                    if artifact.get("id"):
                        result_text += f"图片ID: {artifact['id']}\n"
                    
                    return {
                        "found": True,
                        "result": result_text,
                        "description": f"最新的图片生成物 {artifact.get('id')}",
                        "artifact_info": {
                            "type": "image",
                            "id": artifact.get("id"),
                            "url": artifact.get("url"),
                            "description": artifact.get("description"),
                            "original_content": latest_image.content
                        },
                        "reference_type": "latest_by_type"
                    }
            
            # 3. 检查其他类型的查询
            if any(keyword in task_input.lower() for keyword in ["文档", "document", "text", "文本"]):
                latest_text = self.short_term_memory.find_latest_by_artifact_type("text")
                if latest_text:
                    return {
                        "found": True,
                        "result": f"找到最新的文档: {latest_text.content}",
                        "description": f"最新的文档生成物",
                        "artifact_info": latest_text.artifact,
                        "reference_type": "latest_by_type"
                    }
            
            # 4. 如果是查询历史或上下文的通用请求，提供最近的上下文摘要
            if any(keyword in task_input.lower() for keyword in ["历史", "上下文", "之前", "刚刚", "最近", "previous", "context", "history"]):
                context = self.short_term_memory.get_context_for_llm(5)
                if context and "暂无最近的生成物" not in context:
                    return {
                        "found": True,
                        "result": f"最近的交互上下文:\n{context}",
                        "description": "最近的交互上下文",
                        "artifact_info": None,
                        "reference_type": "context_summary"
                    }
            
            # 没有找到相关内容
            self.logger.info("短期内存中未找到相关内容")
            return {
                "found": False,
                "result": "短期内存中未找到相关内容",
                "description": "无匹配内容"
            }
            
        except Exception as e:
            self.logger.error(f"短期内存搜索失败: {str(e)}")
            return {
                "found": False,
                "result": f"短期内存搜索出错: {str(e)}",
                "description": "搜索错误"
            }
    
    async def _understand_and_call_tool(self, task_input: str) -> Dict[str, Any]:
        """
        理解输入并调用相应的工具
        
        Args:
            task_input: 任务输入
            
        Returns:
            工具调用结果
        """
        try:
            # 构建系统提示词
            system_prompt = self._build_system_prompt()
            
            # 构建用户消息
            user_message = f"请分析以下记忆相关任务并选择合适的工具调用：\n\n任务：{task_input}"

            
            # 准备工具schema（包括think工具）
            available_tools = list(self.memory_tools)
            available_tools.append(get_think_tool_schema())
            
            # 调用LLM进行工具选择
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_message}
            ]
            
            response = self.llm_client.call_llm(
                messages=messages,
                temperature=0.3,
                tools=available_tools,
                tool_choice="auto"
            )
            
            # 处理LLM响应
            if "tool_calls" in response.get("choices", [{}])[0].get("message", {}):
                tool_calls = response["choices"][0]["message"]["tool_calls"]
                
                results = []
                for tool_call in tool_calls:
                    tool_name = tool_call["function"]["name"]
                    tool_args = json.loads(tool_call["function"]["arguments"])
                    
                    self.logger.info(f"调用工具: {tool_name}, 参数: {tool_args}")
                    
                    if tool_name == "think":
                        # 调用think工具
                        think_result = think_tool(tool_args.get("thought", ""))
                        results.append({
                            "tool": tool_name,
                            "result": think_result
                        })
                    else:
                        # 调用记忆工具
                        tool_result = await self._call_memory_tool(tool_name, tool_args)
                        # 对工具结果进行标准化处理
                        processed_result = self._process_tool_result(tool_name, tool_result)
                        results.append({
                            "tool": tool_name,
                            "result": processed_result
                        })
                
                return {
                    "status": "success",
                    "tool_calls": results
                }
            else:
                return {
                    "status": "error",
                    "error": "LLM未返回工具调用"
                }
                        
        except Exception as e:
            self.logger.error(f"理解和调用工具失败: {str(e)}")
            return {
                "status": "error",
                "error": f"工具调用失败: {str(e)}"
            }
    
    def _build_system_prompt(self) -> str:
        """
        构建系统提示词 - 基于从MCP client获取的实际工具schema
        
        Returns:
            系统提示词
        """
        base_prompt = """
你是一个专业的记忆管理助手，负责处理记忆的存储、检索、搜索和删除等任务。

你的工作流程：
1. 根据任务类型选择合适的记忆工具并调用
2. 必须调用具体的记忆工具来完成任务
3. 调用工具后必须调用think工具进行思考

"""
        
        # 动态生成可用工具描述，基于从MCP client获取的实际工具schema
        if self.memory_tools:
            base_prompt += "可用的记忆工具：\n"
            for tool_schema in self.memory_tools:
                tool_func = tool_schema.get("function", {})
                tool_name = tool_func.get("name", "unknown")
                tool_desc = tool_func.get("description", "无描述")
                tool_params = tool_func.get("parameters", {})
                
                base_prompt += f"- {tool_name}: {tool_desc}\n"
                
                # 添加参数信息
                if "properties" in tool_params:
                    param_names = list(tool_params["properties"].keys())
                    if param_names:
                        base_prompt += f"  参数: {', '.join(param_names)}\n"
                
            base_prompt += "\n"
        else:
            base_prompt += "注意：当前没有可用的记忆工具，请检查MCP服务器连接。\n\n"
        
        base_prompt += """
重要规则：
- 根据实际可用的工具选择合适的操作
- 调用记忆工具后调用think工具进行思考
- 工具参数必须严格按照工具schema的要求传递
"""

        return base_prompt
    
    def _process_tool_result(self, tool_name: str, tool_result: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理工具调用结果，按照记忆工具schema进行标准化处理和内容转义
        
        Args:
            tool_name: 工具名称
            tool_result: 原始工具调用结果
            
        Returns:
            处理后的标准化结果
        """
        try:
            if tool_result.get("status") != "success":
                return tool_result
            
            # 获取MCP标准返回的result字段
            mcp_result = tool_result.get("result", {})
            
            # 根据工具类型进行不同的处理
            if tool_name == "search_memories":
                return self._process_search_memories_result(mcp_result)
            elif tool_name == "store_memory":
                return self._process_store_memory_result(mcp_result)
            elif tool_name == "delete_memory":
                return self._process_delete_memory_result(mcp_result)
            elif tool_name == "clear_all_memories":
                return self._process_clear_all_memories_result(mcp_result)
            else:
                # 对于其他工具，进行通用处理
                return self._process_generic_result(mcp_result)
                
        except Exception as e:
            self.logger.error(f"处理工具结果失败: {str(e)}")
            return {
                "status": "error",
                "error": f"结果处理失败: {str(e)}"
            }
    
    def _process_search_memories_result(self, mcp_result: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理search_memories工具的返回结果
        """
        try:
            if "content" in mcp_result:
                content_list = mcp_result["content"]
                if content_list and len(content_list) > 0:
                    content_text = content_list[0].get("text", "")
                    
                    # 解析JSON格式的记忆数据
                    if content_text.strip().startswith('['):
                        import json
                        memories = json.loads(content_text)
                        
                        if memories:
                             # 处理Unicode转义并格式化
                             formatted_memories = []
                             for memory in memories:
                                 if isinstance(memory, str):
                                     # 解码Unicode字符
                                     try:
                                         # 先尝试直接解码
                                         if '\\u' in memory:
                                             decoded_memory = memory.encode('utf-8').decode('unicode_escape')
                                         else:
                                             decoded_memory = memory
                                         formatted_memories.append(decoded_memory)
                                     except Exception as e:
                                         self.logger.warning(f"Unicode解码失败: {str(e)}, 使用原始文本")
                                         formatted_memories.append(memory)
                                 else:
                                     formatted_memories.append(str(memory))
                             
                             return {
                                "status": "success",
                                "result": {
                                    "content": [{
                                        "type": "text",
                                        "text": f"找到 {len(formatted_memories)} 条记忆:\n" + "\n".join([f"{i+1}. {mem}" for i, mem in enumerate(formatted_memories)])
                                    }],
                                    "isError": False
                                }
                            }
                        else:
                            return {
                                "status": "success",
                                "result": {
                                    "content": [{
                                        "type": "text",
                                        "text": "未找到相关记忆"
                                    }],
                                    "isError": False
                                }
                            }
                    else:
                        # 非JSON格式，直接返回
                        return {
                            "status": "success",
                            "result": {
                                "content": [{
                                    "type": "text",
                                    "text": content_text
                                }],
                                "isError": False
                            }
                        }
                else:
                    return {
                        "status": "success",
                        "result": {
                            "content": [{
                                "type": "text",
                                "text": "查询结果为空"
                            }],
                            "isError": False
                        }
                    }
            else:
                return {
                    "status": "success",
                    "result": {
                        "content": [{
                            "type": "text",
                            "text": str(mcp_result)
                        }],
                        "isError": False
                    }
                }
                
        except Exception as e:
            self.logger.error(f"处理search_memories结果失败: {str(e)}")
            return {
                "status": "error",
                "error": f"search_memories结果处理失败: {str(e)}"
            }
    
    def _process_store_memory_result(self, mcp_result: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理store_memory工具的返回结果
        """
        try:
            if "content" in mcp_result:
                content_list = mcp_result["content"]
                if content_list and len(content_list) > 0:
                    content_text = content_list[0].get("text", "")
                    return {
                        "status": "success",
                        "result": {
                            "content": [{
                                "type": "text",
                                "text": f"记忆存储成功: {content_text}"
                            }],
                            "isError": False
                        }
                    }
            
            return {
                "status": "success",
                "result": {
                    "content": [{
                        "type": "text",
                        "text": "记忆存储成功"
                    }],
                    "isError": False
                }
            }
            
        except Exception as e:
            self.logger.error(f"处理store_memory结果失败: {str(e)}")
            return {
                "status": "error",
                "error": f"store_memory结果处理失败: {str(e)}"
            }
    
    def _process_delete_memory_result(self, mcp_result: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理delete_memory工具的返回结果
        """
        try:
            if "content" in mcp_result:
                content_list = mcp_result["content"]
                if content_list and len(content_list) > 0:
                    content_text = content_list[0].get("text", "")
                    return {
                        "status": "success",
                        "result": {
                            "content": [{
                                "type": "text",
                                "text": f"记忆删除成功: {content_text}"
                            }],
                            "isError": False
                        }
                    }
            
            return {
                "status": "success",
                "result": {
                    "content": [{
                        "type": "text",
                        "text": "记忆删除成功"
                    }],
                    "isError": False
                }
            }
            
        except Exception as e:
            self.logger.error(f"处理delete_memory结果失败: {str(e)}")
            return {
                "status": "error",
                "error": f"delete_memory结果处理失败: {str(e)}"
            }
    
    def _process_generic_result(self, mcp_result: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理通用工具结果
        """
        try:
            if "content" in mcp_result:
                content_list = mcp_result["content"]
                if content_list and len(content_list) > 0:
                    content_text = content_list[0].get("text", "")
                    # 尝试解码Unicode字符
                    try:
                        decoded_text = content_text.encode().decode('unicode_escape')
                        content_text = decoded_text
                    except:
                        pass  # 如果解码失败，使用原始文本
                    
                    return {
                        "status": "success",
                        "result": {
                            "content": [{
                                "type": "text",
                                "text": content_text
                            }],
                            "isError": False
                        }
                    }
            
            return {
                "status": "success",
                "result": {
                    "content": [{
                        "type": "text",
                        "text": str(mcp_result)
                    }],
                    "isError": False
                }
            }
            
        except Exception as e:
            self.logger.error(f"处理通用结果失败: {str(e)}")
            return {
                "status": "error",
                "error": f"通用结果处理失败: {str(e)}"
            }
    
    async def _call_memory_tool(self, tool_name: str, tool_args: Dict[str, Any]) -> Dict[str, Any]:
        """
        调用记忆工具 - 直接使用MCP客户端的标准function call
        
        Args:
            tool_name: 工具名称
            tool_args: 工具参数
            
        Returns:
            工具调用结果
        """
        try:
            # 直接调用MCP工具，不做任何硬编码解析
            result = await self.mcp_client.call_tool(tool_name, tool_args)
            self.logger.info(f"MCP工具 {tool_name} 调用成功")
            return result
                
        except Exception as e:
            self.logger.error(f"调用记忆工具 {tool_name} 失败: {str(e)}")
            return {
                "status": "error",
                "error": f"工具调用失败: {str(e)}"
            }
    

    


    
    def cleanup(self):
        """
        清理资源
        """
        if self.mcp_client:
            self.mcp_client.cleanup()






