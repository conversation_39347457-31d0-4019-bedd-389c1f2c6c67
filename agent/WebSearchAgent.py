# 这是一个专门用于在网络上搜索信息并处理的 agent
# 搜索使用 duckduckgo实现

import json
from typing import List, Dict, Any, Optional
from datetime import datetime
import sys
import os
import logging

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from ddgs import DDGS
from bs4 import BeautifulSoup
import requests
from util.llmcallqianwen import create_llm_client
from mcpclient.ThinkTool import think_tool, get_think_tool_schema


class SearchResult:
    """搜索结果类"""
    def __init__(self, title: str, url: str, snippet: str, source: str = "web"):
        self.title = title
        self.url = url
        self.snippet = snippet
        self.source = source
        self.timestamp = datetime.now().isoformat()
        
    def to_dict(self) -> Dict[str, Any]:
        return {
            "title": self.title,
            "url": self.url,
            "snippet": self.snippet,
            "source": self.source,
            "timestamp": self.timestamp
        }
    
    def __str__(self) -> str:
        return f"[{self.title}]({self.url})\n{self.snippet}"


class WebSearchAgent:
    """网络搜索Agent - 改造后可以接受PlanAgent分发的任务
    具备理解输入、调用工具、思考反思的能力
    """
    
    def __init__(self, max_results: int = 10, timeout: int = 30):
        """
        初始化WebSearchAgent
        
        Args:
            max_results: 最大搜索结果数量
            timeout: 搜索超时时间（秒）
        """
        self.max_results = max_results
        self.timeout = timeout
        self.ddgs = DDGS()
        self.search_history: List[Dict[str, Any]] = []
        self.llm_client = create_llm_client()  # LLM客户端
        self.logger = logging.getLogger(__name__)
        
        # 定义搜索工具的schema（符合OpenAI function calling格式）
        self.search_tools = [
            {
                "type": "function",
                "function": {
                    "name": "search_web",
                    "description": "在网络上搜索信息，返回相关的搜索结果",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "query": {
                                "type": "string",
                                "description": "搜索查询关键词"
                            },
                            "max_results": {
                                "type": "integer",
                                "description": "最大搜索结果数量",
                                "default": 10
                            }
                        },
                        "required": ["query"]
                    }
                }
            },
            {
                "type": "function",
                "function": {
                    "name": "search_news",
                    "description": "搜索新闻信息，返回相关的新闻结果",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "query": {
                                "type": "string",
                                "description": "新闻搜索查询关键词"
                            },
                            "max_results": {
                                "type": "integer",
                                "description": "最大搜索结果数量",
                                "default": 10
                            }
                        },
                        "required": ["query"]
                    }
                }
            },
            {
                "type": "function",
                "function": {
                    "name": "extract_content_from_url",
                    "description": "从指定URL提取网页内容",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "url": {
                                "type": "string",
                                "description": "要提取内容的网页URL"
                            },
                            "max_length": {
                                "type": "integer",
                                "description": "最大内容长度",
                                "default": 2000
                            }
                        },
                        "required": ["url"]
                    }
                }
            }
        ]
        
    def search_web(self, query: str, max_results: Optional[int] = None) -> List[SearchResult]:
        """
        使用DuckDuckGo搜索网络信息
        
        Args:
            query: 搜索查询
            max_results: 最大结果数量，如果为None则使用默认值
            
        Returns:
            搜索结果列表
        """
        if max_results is None:
            max_results = self.max_results
            
        results = []
        
        try:
            # 记录搜索历史
            search_record = {
                "query": query,
                "timestamp": datetime.now().isoformat(),
                "status": "started"
            }
            
            # 使用DuckDuckGo搜索
            ddg_results = self.ddgs.text(
                query,
                max_results=max_results,
                safesearch='moderate',
                timelimit=None
            )
            
            for result in ddg_results:
                search_result = SearchResult(
                    title=result.get('title', ''),
                    url=result.get('href', ''),
                    snippet=result.get('body', ''),
                    source="duckduckgo"
                )
                results.append(search_result)
            
            search_record["status"] = "completed"
            search_record["results_count"] = len(results)
            self.search_history.append(search_record)
            
        except Exception as e:
            search_record["status"] = "failed"
            search_record["error"] = str(e)
            self.search_history.append(search_record)
            print(f"搜索失败: {str(e)}")
            
        return results
    
    def search_news(self, query: str, max_results: Optional[int] = None) -> List[SearchResult]:
        """
        搜索新闻信息
        
        Args:
            query: 搜索查询
            max_results: 最大结果数量
            
        Returns:
            新闻搜索结果列表
        """
        if max_results is None:
            max_results = min(self.max_results, 5)  # 新闻搜索默认较少结果
            
        results = []
        
        try:
             # 使用DuckDuckGo新闻搜索
             news_results = self.ddgs.news(
                 query,
                 max_results=max_results,
                 safesearch='moderate'
             )
             
             for result in news_results:
                search_result = SearchResult(
                    title=result.get('title', ''),
                    url=result.get('url', ''),
                    snippet=result.get('body', ''),
                    source="news"
                )
                results.append(search_result)
                
        except Exception as e:
            print(f"新闻搜索失败: {str(e)}")
            
        return results
    
    def extract_content_from_url(self, url: str, max_length: int = 2000) -> str:
        """
        从URL提取内容
        
        Args:
            url: 目标URL
            max_length: 最大内容长度
            
        Returns:
            提取的文本内容
        """
        try:
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }
            
            response = requests.get(url, headers=headers, timeout=self.timeout)
            response.raise_for_status()
            
            # 使用BeautifulSoup解析HTML
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # 移除脚本和样式元素
            for script in soup(["script", "style"]):
                script.decompose()
            
            # 提取文本
            text = soup.get_text()
            
            # 清理文本
            lines = (line.strip() for line in text.splitlines())
            chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
            text = ' '.join(chunk for chunk in chunks if chunk)
            
            # 限制长度
            if len(text) > max_length:
                text = text[:max_length] + "..."
                
            return text
            
        except Exception as e:
            return f"无法提取内容: {str(e)}"

    
    async def process_task(self, task_input: str) -> Dict[str, Any]:
        """
        使用标准ReAct模式处理搜索任务
        流程：Thought -> Action -> Observation -> (循环) -> Final Answer
        
        Args:
            task_input: 任务输入描述
            
        Returns:
            处理结果
        """
        try:
            self.logger.info(f"开始处理搜索任务: {task_input}")
            
            # 初始化ReAct循环变量
            max_iterations = 3
            iteration = 0
            conversation_history = []
            final_answer = None
            
            # 构建初始系统提示词
            system_prompt = self._build_react_system_prompt()
            
            while iteration < max_iterations and not final_answer:
                iteration += 1
                self.logger.info(f"ReAct循环第{iteration}轮")
                
                # 构建当前轮次的消息
                messages = [{
                    "role": "system", 
                    "content": system_prompt
                }]
                
                # 添加对话历史
                messages.extend(conversation_history)
                
                # 添加当前任务（第一轮）或继续思考（后续轮次）
                if iteration == 1:
                    messages.append({
                        "role": "user",
                        "content": f"请分析以下任务并开始ReAct循环：\n\n任务：{task_input}"
                    })
                else:
                    messages.append({
                        "role": "user",
                        "content": "请继续思考并决定下一步行动，或者给出最终答案。"
                    })
                
                # 准备工具schema（包括think工具）
                available_tools = list(self.search_tools)
                available_tools.append(get_think_tool_schema())
                
                # 调用LLM
                response = self.llm_client.call_llm(
                    messages=messages,
                    temperature=0.3,
                    tools=available_tools,
                    tool_choice="auto"
                )
                
                # 处理LLM响应
                message = response.get("choices", [{}])[0].get("message", {})
                content = message.get("content", "")
                tool_calls = message.get("tool_calls", [])
                
                # 添加助手的响应到对话历史（包含工具调用）
                assistant_message = {
                    "role": "assistant",
                    "content": content
                }
                if tool_calls:
                    assistant_message["tool_calls"] = tool_calls
                
                conversation_history.append(assistant_message)
                
                if content:
                    self.logger.info(f"Thought: {content}")
                
                # 处理工具调用
                if tool_calls:
                    for tool_call in tool_calls:
                        tool_name = tool_call["function"]["name"]
                        tool_args = json.loads(tool_call["function"]["arguments"])
                        
                        self.logger.info(f"Action: {tool_name}({tool_args})")
                        
                        # 执行工具调用
                        if tool_name == "think":
                            think_result = think_tool(tool_args.get("thought", ""))
                            tool_result_content = json.dumps(think_result, ensure_ascii=False)
                        else:
                            # 执行搜索工具
                            search_result = await self._call_search_tool(tool_name, tool_args)
                            tool_result_content = json.dumps(search_result, ensure_ascii=False)
                            
                            # 搜索工具调用后自动调用think工具
                            think_result = think_tool(f"刚刚调用了{tool_name}工具，结果是：{json.dumps(search_result, ensure_ascii=False)}")
                            self.logger.info(f"Auto Think: {think_result}")
                        
                        # 添加每个工具调用的结果到对话历史
                        conversation_history.append({
                            "role": "tool",
                            "tool_call_id": tool_call["id"],
                            "content": tool_result_content
                        })
                        
                        self.logger.info(f"Observation: {tool_result_content[:200]}...")
                    
                else:
                    # 没有工具调用，检查是否给出了最终答案
                    if "最终答案" in content or "Final Answer" in content or iteration >= max_iterations:
                        final_answer = content
                        break
            
            # 如果没有明确的最终答案，生成一个
            if not final_answer:
                final_answer = await self._generate_final_answer(task_input, conversation_history)
            
            # 返回结果
            return {
                "status": "success",
                "task_input": task_input,
                "result": final_answer,
                "iterations": iteration,
                "conversation_history": conversation_history,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"处理搜索任务异常: {str(e)}")
            return {
                "status": "error",
                "error": f"任务处理异常: {str(e)}",
                "task_input": task_input
            }
    

    
    def _build_react_system_prompt(self) -> str:
        """
        构建ReAct模式的系统提示词
        
        Returns:
            ReAct系统提示词
        """
        return """
你是一个智能搜索助手，使用ReAct（Reasoning and Acting）模式来处理用户的搜索任务。

## ReAct工作流程：
1. **Thought（思考）**: 分析当前情况，思考下一步应该做什么
2. **Action（行动）**: 选择并执行合适的工具
3. **Observation（观察）**: 观察工具执行的结果
4. **重复循环**: 根据观察结果继续思考，直到能给出最终答案

## 可用工具：
- `search_web`: 在网络上搜索一般信息
- `search_news`: 搜索新闻信息  
- `extract_content_from_url`: 从指定URL提取内容
- `think`: 思考和分析工具（用于深度思考和推理）

## 重要规则：
1. 每次调用搜索工具后，系统会自动调用think工具进行分析
2. 在每轮循环中，先进行思考（Thought），然后决定行动（Action）
3. 观察工具结果后，继续思考是否需要更多信息或可以给出最终答案
4. 当你认为已经收集到足够信息时，请明确说明"最终答案"或"Final Answer"
5. 保持思考过程清晰，让用户能够跟踪你的推理过程

请按照ReAct模式处理用户任务，展示完整的思考-行动-观察循环。
        """.strip()
    
    def _build_system_prompt(self, previous_attempt: Optional[Dict] = None, reflection_feedback: str = "") -> str:
        """
        构建传统系统提示词（保留兼容性）
        
        Args:
            previous_attempt: 之前的尝试结果
            reflection_feedback: 反思反馈
            
        Returns:
            系统提示词
        """
        base_prompt = """
你是一个智能搜索助手，能够理解用户的搜索需求并选择合适的工具来获取信息。

可用工具：
1. search_web - 在网络上搜索一般信息
2. search_news - 搜索新闻信息
3. extract_content_from_url - 从指定URL提取内容
4. think - 思考和分析工具

请根据用户的任务描述，选择最合适的工具并提供正确的参数。
每次调用搜索工具后，必须调用think工具进行思考分析。
        """.strip()
        
        if previous_attempt:
            base_prompt += "\n\n注意：之前的尝试可能不够准确，请根据反思反馈调整策略。"
        
        if reflection_feedback:
            base_prompt += f"\n\n反思反馈：{reflection_feedback}"
        
        return base_prompt
    
    async def _call_search_tool(self, tool_name: str, tool_args: Dict[str, Any]) -> Dict[str, Any]:
        """
        调用搜索工具
        
        Args:
            tool_name: 工具名称
            tool_args: 工具参数
            
        Returns:
            工具调用结果
        """
        try:
            if tool_name == "search_web":
                results = self.search_web(
                    query=tool_args.get("query", ""),
                    max_results=tool_args.get("max_results", self.max_results)
                )
                return {
                    "tool": tool_name,
                    "result": [result.to_dict() for result in results],
                    "status": "success",
                    "query": tool_args.get("query", "")
                }
            
            elif tool_name == "search_news":
                results = self.search_news(
                    query=tool_args.get("query", ""),
                    max_results=tool_args.get("max_results", self.max_results)
                )
                return {
                    "tool": tool_name,
                    "result": [result.to_dict() for result in results],
                    "status": "success",
                    "query": tool_args.get("query", "")
                }
            
            elif tool_name == "extract_content_from_url":
                content = self.extract_content_from_url(
                    url=tool_args.get("url", ""),
                    max_length=tool_args.get("max_length", 2000)
                )
                return {
                    "tool": tool_name,
                    "result": content,
                    "status": "success",
                    "url": tool_args.get("url", "")
                }
            
            else:
                return {
                    "tool": tool_name,
                    "result": None,
                    "status": "error",
                    "error": f"未知工具: {tool_name}"
                }
                
        except Exception as e:
            return {
                "tool": tool_name,
                "result": None,
                "status": "error",
                "error": str(e)
            }
    

    
    async def _generate_final_answer(self, task_input: str, conversation_history: List[Dict[str, Any]]) -> str:
        """
        基于对话历史生成最终答案
        
        Args:
            task_input: 原始任务输入
            conversation_history: 对话历史
            
        Returns:
            最终答案
        """
        try:
            # 构建最终答案生成提示词
            final_prompt = f"""
基于以下ReAct循环的对话历史，为用户的任务生成一个清晰、完整的最终答案：

原始任务：{task_input}

对话历史：{json.dumps(conversation_history, ensure_ascii=False, indent=2)}

请提供：
1. 对原始任务的直接回答
2. 基于搜索结果的关键信息总结
3. 如果有多个相关结果，请整合最重要的信息

请以自然语言回复，内容要准确、简洁、有用。
            """.strip()
            
            messages = [
                {"role": "user", "content": final_prompt}
            ]
            
            response = self.llm_client.call_llm(
                messages=messages,
                temperature=0.3
            )
            
            # 获取LLM生成的最终答案
            final_answer = response.get("choices", [{}])[0].get("message", {}).get("content", "")
            return final_answer
            
        except Exception as e:
            self.logger.error(f"生成最终答案异常: {str(e)}")
            return f"抱歉，在生成最终答案时出现异常：{str(e)}"
    
    async def _parse_final_result(self, task_input: str, tool_result: Dict[str, Any]) -> Dict[str, Any]:
        """
        解析和格式化最终结果（保留兼容性）
        
        Args:
            task_input: 原始任务输入
            tool_result: 工具调用结果
            
        Returns:
            格式化的最终结果
        """
        try:
            # 构建结果解析提示词
            parse_prompt = f"""
请分析以下搜索任务和工具调用结果，并生成一个清晰、有用的总结：

原始任务：{task_input}

工具调用结果：{json.dumps(tool_result, ensure_ascii=False, indent=2)}

请提供：
1. 任务完成情况的简要说明
2. 主要发现或结果的总结
3. 如果有多个搜索结果，请提取最相关的信息

请以自然语言回复，内容要简洁明了。
            """.strip()
            
            messages = [
                {"role": "user", "content": parse_prompt}
            ]
            
            response = self.llm_client.call_llm(
                messages=messages,
                temperature=0.3
            )
            
            # 获取LLM生成的总结
            summary = response.get("choices", [{}])[0].get("message", {}).get("content", "")
            
            return {
                "status": "success",
                "task_input": task_input,
                "summary": summary,
                "raw_results": tool_result,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"解析最终结果异常: {str(e)}")
            return {
                "status": "error",
                "task_input": task_input,
                "error": f"结果解析异常: {str(e)}",
                "raw_results": tool_result,
                "timestamp": datetime.now().isoformat()
            }
