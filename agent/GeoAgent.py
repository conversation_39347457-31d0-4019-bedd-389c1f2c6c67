import json
import os
import sys
from typing import Dict, List, Any
import logging


# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from mcpclient.McpClient import McpClient
from util.llmcallqianwen import create_llm_client
from mcpclient.ThinkTool import get_think_tool_schema, think_tool



class GeoAgent:
    """智能地理Agent - 负责地理天气、路径规划查询的智能代理"""
    
    def __init__(self, config_file: str = "mcpclient/tool.json"):
        """
        初始化智能地理Agent
        
        Args:
            config_file: MCP配置文件路径
        """
        self.mcp_client = McpClient(config_file)
        self.llm_client = create_llm_client()  # LLM客户端
        self.logger = logging.getLogger(__name__)
        self.geo_server_id = None  # 地理服务器ID
        self.is_initialized = False
        self.available_tools = {}  # 存储可用工具的schema
        self.geo_tools = {}  # 存储地理相关工具的schema
        
    async def initialize(self) -> bool:
        """
        初始化地理Agent，发现MCP地理服务并获取工具schema
        
        Returns:
            是否初始化成功
        """
        try:
            # 发现MCP工具
            success = await self.mcp_client.discover_tools()
            if not success:
                self.logger.error("MCP工具发现失败")
                return False
            
            # 存储所有可用工具的schema
            self.available_tools = {}
            self.geo_tools = {}
            
            for tool_key, tool in self.mcp_client.tools.items():
                # 构建工具schema
                tool_schema = {
                    "name": tool.name,
                    "description": tool.description,
                    "parameters": tool.input_schema
                }
                self.available_tools[tool_key] = tool_schema
                
                # 识别地理相关工具
                if any(keyword in tool.name.lower() for keyword in ['geo', 'map', 'location', 'weather', 'route', 'amap', 'navi', 'taxi']):
                    self.geo_tools[tool_key] = tool_schema
                    if not self.geo_server_id:
                        self.geo_server_id = tool_key.split(':')[0]
            
            if not self.geo_tools:
                self.logger.warning("未找到地理相关工具")
                return False
            
            self.logger.info(f"发现 {len(self.geo_tools)} 个地理工具:")
            for tool_key, tool_schema in self.geo_tools.items():
                self.logger.info(f"  - {tool_key}: {tool_schema['name']}")
                self.logger.info(f"    描述: {tool_schema['description']}")
            
            self.is_initialized = True
            return True
            
        except Exception as e:
            self.logger.error(f"地理Agent初始化失败: {str(e)}")
            return False
    

    async def process_task(self, task_input: str) -> Dict[str, Any]:
        """
        使用React模式处理PlanAgent分发的地理任务
        
        Args:
            task_input: 任务输入描述
            
        Returns:
            处理结果
        """
        try:
            if not self.is_initialized:
                await self.initialize()
            
            # 执行React循环
            react_result = await self._react_loop(task_input)
            
            return {
                "status": "success",
                "result": react_result,
                "task_input": task_input
            }
            
        except Exception as e:
            self.logger.error(f"任务处理失败: {str(e)}")
            return {
                "status": "error",
                "error": str(e),
                "task_input": task_input
            }
    
    async def _react_loop(self, task_input: str, max_iterations: int = 5) -> str:
        """
        执行React循环：Thought-Action-Observation
        
        Args:
            task_input: 任务输入
            max_iterations: 最大迭代次数
            
        Returns:
            最终答案
        """
        conversation_history = []
        
        # 构建初始系统提示
        system_prompt = self._build_react_system_prompt()
        conversation_history.append({"role": "system", "content": system_prompt})
        conversation_history.append({"role": "user", "content": task_input})
        
        for iteration in range(max_iterations):
            self.logger.info(f"React循环第{iteration + 1}轮")
            
            # 准备工具schema
            tools = self._prepare_react_tools()
            
            try:
                # 调用LLM
                llm_response = self.llm_client.call_llm(
                    messages=conversation_history,
                    temperature=0.1,
                    tools=tools,
                    tool_choice="auto"
                )
                
                assistant_message = llm_response['choices'][0]['message']
                content = assistant_message.get('content', '')
                tool_calls = assistant_message.get('tool_calls', [])
                
                # 添加assistant消息到历史
                conversation_history.append({
                    "role": "assistant",
                    "content": content,
                    "tool_calls": tool_calls if tool_calls else None
                })
                
                # 如果没有工具调用，说明得到了最终答案
                if not tool_calls:
                    self.logger.info(f"React循环完成，最终答案: {content}")
                    return content
                
                # 处理工具调用
                for tool_call in tool_calls:
                    tool_name = tool_call['function']['name']
                    tool_params = json.loads(tool_call['function']['arguments'])
                    tool_call_id = tool_call['id']
                    
                    self.logger.info(f"执行工具: {tool_name}, 参数: {tool_params}")
                    
                    # 执行工具
                    if tool_name == "think":
                        tool_result = think_tool(tool_params.get('thought', ''))
                        result_content = f"思考结果: {tool_result}"
                    else:
                        # 执行地理工具
                        tool_result = await self._call_mcp_tool(tool_name, tool_params)
                        result_content = json.dumps(tool_result, ensure_ascii=False, indent=2)
                    
                    # 添加工具结果到历史
                    conversation_history.append({
                        "role": "tool",
                        "tool_call_id": tool_call_id,
                        "content": result_content
                    })
                
            except Exception as e:
                self.logger.error(f"React循环第{iteration + 1}轮失败: {str(e)}")
                return f"处理过程中出现错误: {str(e)}"
        
        # 如果达到最大迭代次数仍未完成
        return "任务处理超时，未能在规定轮次内完成。"
    
    def _build_react_system_prompt(self) -> str:
        """
        构建React模式的系统提示
        """
        # 构建工具列表描述
        tool_descriptions = []
        
        # 添加think工具
        tool_descriptions.append("- think: 进行思考和推理")
        
        # 添加地理工具描述
        for tool_key, tool_schema in self.geo_tools.items():
            tool_name = tool_schema['name']
            tool_desc = tool_schema['description']
            tool_descriptions.append(f"- {tool_name}: {tool_desc}")
        
        tools_text = "\n".join(tool_descriptions)
        
        return f"""
你是一个智能地理助手，使用React模式来解决地理相关问题。

你有以下工具可以使用：
{tools_text}

请按照以下React模式工作：

1. **Thought**: 分析问题，思考需要做什么
2. **Action**: 选择合适的工具并执行
3. **Observation**: 观察工具执行结果
4. 重复上述过程直到能够给出最终答案

重要规则：
- 每次只能调用一个工具
- 使用think工具来进行推理和分析
- 当你有足够信息回答用户问题时，直接给出最终答案，不要调用工具
- 最终答案要简洁、准确、用户友好

天气建议规则：
- 只有在天气预报明确显示有降水（雨、雪、雷阵雨等）时才建议携带雨具
- 阴天、多云等无降水天气不需要建议携带雨具
- 根据实际天气状况给出合理的穿衣和出行建议
- 避免给出与实际天气不符的建议

开始处理用户的地理问题吧！
"""
    
    def _prepare_react_tools(self) -> List[Dict[str, Any]]:
        """
        准备React模式的工具schema
        """
        tools = []
        
        # 添加think工具
        tools.append(get_think_tool_schema())
        
        # 添加从MCP client获取的地理工具
        for tool_key, tool_schema in self.geo_tools.items():
            # 转换为LLM工具调用格式
            llm_tool_schema = {
                "type": "function",
                "function": {
                    "name": tool_schema['name'],
                    "description": tool_schema['description'],
                    "parameters": tool_schema['parameters']
                }
            }
            tools.append(llm_tool_schema)
        
        return tools
    
    async def _call_mcp_tool(self, tool_name: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        调用MCP工具
        
        Args:
            tool_name: 工具名称
            params: 工具参数
            
        Returns:
            工具调用结果
        """
        try:
            # 查找匹配的工具
            target_tool = None
            
            # 首先尝试精确匹配
            for tool_key, tool in self.mcp_client.tools.items():
                if tool.name == tool_name:
                    target_tool = tool_key
                    break
            
            # 如果精确匹配失败，尝试模糊匹配
            if not target_tool:
                for tool_key, tool in self.mcp_client.tools.items():
                    # 检查工具名称是否包含查询的工具名
                    if tool_name.lower() in tool.name.lower():
                        target_tool = tool_key
                        break
                    # 检查是否是地理相关工具的关键词匹配
                    if tool_key in self.geo_tools:
                        tool_keywords = ['geo', 'map', 'location', 'weather', 'route', 'search', 'navi', 'taxi']
                        if any(keyword in tool_name.lower() for keyword in tool_keywords) and \
                           any(keyword in tool.name.lower() for keyword in tool_keywords):
                            target_tool = tool_key
                            break
            
            if not target_tool:
                available_tools = [tool.name for tool in self.mcp_client.tools.values()]
                self.logger.error(f"未找到工具: {tool_name}，可用工具: {available_tools}")
                return {
                    "status": "error", 
                    "error": f"未找到工具: {tool_name}，可用工具: {available_tools}"
                }
            
            self.logger.info(f"调用工具: {target_tool} 参数: {params}")
            
            # 调用工具 - 使用工具的简单名称而不是完整的key
            actual_tool_name = self.mcp_client.tools[target_tool].name
            result = await self.mcp_client.call_tool(actual_tool_name, params)
            return {"status": "success", "data": result}
            
        except Exception as e:
            self.logger.error(f"MCP工具调用失败: {str(e)}")
            return {"status": "error", "error": str(e)}
    

    def cleanup(self):
        """清理资源"""
        if self.mcp_client:
            self.mcp_client.cleanup()
