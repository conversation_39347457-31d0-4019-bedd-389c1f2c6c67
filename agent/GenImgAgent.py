"""
GenImgAgent - 图像生成和编辑智能体
=====================================

这是Agentic RAG系统中专门处理图像相关任务的智能体。

核心功能：
1. 单张图像生成 - 根据用户的文本描述生成高质量的单张图像
2. 连续图像生成 - 根据故事情节生成多张保持一致性的连贯图像
3. 图像编辑 - 根据用户的编辑指令对现有图像进行精确修改

技术架构：
- 基于DashScope的图像生成和编辑API
- 集成LLM进行任务分析和提示词优化
- 支持OpenAI Function Calling格式的工具调用
- 提供完整的任务历史记录和状态管理

设计特点：
- 智能任务分析：自动识别单张生成、连续生成、图像编辑任务类型
- 一致性保证：连续图像通过图像编辑技术确保角色和风格一致性
- 鲁棒性处理：提供默认场景和错误降级机制
- 可扩展性：支持多种图像模型和参数配置
"""

import json
import re
import sys
import os
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime

# 添加项目根目录到路径，确保能正确导入项目模块
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 项目内部模块
from util.gen_img import ImageGenerator  # 图像生成和编辑的底层API封装
from util.llmcallqianwen import create_llm_client  # LLM客户端，用于任务分析和提示词优化


class GenImgAgent:
    """
    图像生成和编辑Agent - 专门处理图像相关任务
    
    核心功能：
    1. 单张图像生成 - 根据用户的文本描述生成单张图像
    2. 连续图像生成 - 根据故事情节生成多张连贯的图像
    3. 图像编辑 - 根据用户的编辑指令对现有图像进行修改
    
    该Agent具备理解用户需求、分解复杂任务、生成高质量连贯图像的能力
    """
    
    def __init__(self):
        """
        初始化GenImgAgent
        
        设置核心组件：
        1. 图像生成器 - 封装DashScope API调用
        2. LLM客户端 - 用于任务分析和提示词优化  
        3. 日志记录器 - 跟踪执行过程和错误
        4. 生成历史 - 保存所有任务执行记录
        
        注意：当前实现采用任务分析+直接API调用的方式，
        而非LLM Function Calling机制。
        """
        # 核心组件初始化
        self.image_generator = ImageGenerator()  # DashScope图像API的封装
        self.llm_client = create_llm_client()  # 千问LLM客户端
        self.logger = logging.getLogger(__name__)  # 日志记录器
        self.generation_history: List[Dict[str, Any]] = []  # 任务执行历史
        

    
    async def _analyze_task(self, task_input: str) -> Dict[str, Any]:
        """
        智能分析图像任务类型和需求
        
        使用LLM分析用户输入，自动识别任务类型：
        1. single_generation: 单张独立图像生成
        2. sequential_generation: 连续图像生成（故事、教程等）
        3. image_edit: 现有图像编辑
        
        通过关键词识别、上下文理解等方式准确分类任务，
        并提取风格要求、特殊需求等关键信息。
        
        Args:
            task_input: 用户的任务描述，可能包含复杂的自然语言表达
            
        Returns:
            Dict包含：
            - task_type: 识别的任务类型
            - description: 任务的标准化描述
            - scene_count: 场景数量（连续生成时）
            - style_requirements: 提取的风格要求
            - special_requirements: 特殊要求和约束
            - original_input: 原始输入（用于后续处理）
        """
        # 构建任务分析的提示词
        analysis_prompt = f"""
请分析以下图像生成任务，判断任务类型并提取关键信息：

任务描述：{task_input}

请按照以下JSON格式返回分析结果：
{{
    "task_type": "single_generation" | "sequential_generation" | "image_edit",
    "description": "任务的详细描述",
    "scene_count": 数字（仅当task_type为sequential_generation时），
    "style_requirements": "风格要求",
    "special_requirements": "特殊要求"
}}

判断标准：
- single_generation: 生成单张独立图像
- sequential_generation: 需要生成多张连贯图像（如故事、教程、连续场景等）
- image_edit: 编辑现有图像
"""
        
        try:
            # 调用LLM进行任务分析，使用较低温度确保稳定输出
            response = self.llm_client.call_llm(
                messages=[{"role": "user", "content": analysis_prompt}],
                temperature=0.1  # 低温度确保分析结果稳定
            )
            
            # 提取LLM回复内容
            content = response['choices'][0]['message']['content']
            
            # 鲁棒的JSON提取 - 处理LLM可能返回的各种格式
            json_match = re.search(r'\{.*?\}', content, re.DOTALL)
            if json_match:
                json_str = json_match.group()
                # 清理格式问题：移除换行符和多余空格
                json_str = json_str.replace('\n', ' ').replace('\r', ' ')
                json_str = re.sub(r'\s+', ' ', json_str)
                
                # 解析JSON并添加原始输入
                result = json.loads(json_str)
                result['original_input'] = task_input
                return result
            else:
                # 备用方案：尝试直接解析整个回复
                try:
                    result = json.loads(content.strip())
                    result['original_input'] = task_input
                    return result
                except:
                    # 最终降级：默认为单张生成
                    return {
                        "task_type": "single_generation",
                        "description": task_input,
                        "style_requirements": "",
                        "special_requirements": "",
                        "original_input": task_input
                    }
                
        except Exception as e:
            # 异常处理：任务分析失败时的降级策略
            self.logger.error(f"任务分析失败: {str(e)}")
            return {
                "task_type": "single_generation",  # 默认为最简单的单张生成
                "description": task_input,
                "style_requirements": "",
                "special_requirements": "",
                "original_input": task_input
            }
    
    async def _decompose_story(self, story_description: str, scene_count: int) -> List[str]:
        """
        智能分解故事为连贯场景序列
        
        使用LLM将完整的故事描述分解为多个简洁的场景描述，
        每个场景都适合作为图像生成的提示词。
        
        核心特性：
        1. 场景连贯性：确保场景之间有逻辑联系
        2. 视觉化：每个场景突出关键的视觉元素和动作
        3. 简洁性：控制每个场景描述在20-30字以内
        4. 鲁棒性：提供默认的"乌鸦喝水"场景作为降级方案
        
        Args:
            story_description: 完整的故事描述
            scene_count: 需要分解的场景数量
            
        Returns:
            List[str]: 场景描述列表，每个元素是一个简洁的场景描述
            
        Note:
            包含"乌鸦喝水"默认场景作为few-shot示例和降级方案，
            这是经过验证的、适合连续图像生成的经典故事场景。
        """
        decompose_prompt = f"""
请将以下故事分解为{scene_count}个连贯的场景，每个场景用一句简洁的话描述关键动作：

故事：{story_description}

要求：
1. 每个场景描述要简洁明了（20-30字以内）
2. 突出关键动作和视觉元素
3. 场景之间要有逻辑连贯性
4. 适合作为图像生成的提示词

请严格按照以下格式返回：
场景1：[简洁的动作描述]
场景2：[简洁的动作描述]
场景3：[简洁的动作描述]
场景4：[简洁的动作描述]

示例格式（经典的"乌鸦喝水"故事，作为few-shot学习示例）：
场景1：乌鸦在炎热的天空中飞行寻找水源
场景2：乌鸦发现瓶子但够不到水
"""
        
        try:
            response = self.llm_client.call_llm(
                messages=[{"role": "user", "content": decompose_prompt}],
                temperature=0.3
            )
            
            content = response['choices'][0]['message']['content']
            self.logger.info(f"故事分解LLM返回内容: {content}")
            
            # 提取场景描述
            scenes = []
            lines = content.split('\n')
            for line in lines:
                line = line.strip()
                if line and ('场景' in line or '第' in line):
                    # 提取冒号后的内容
                    if '：' in line:
                        scene_desc = line.split('：', 1)[1].strip()
                        if scene_desc and len(scene_desc) > 5:  # 确保描述不为空
                            scenes.append(scene_desc)
                    elif ':' in line:
                        scene_desc = line.split(':', 1)[1].strip()
                        if scene_desc and len(scene_desc) > 5:
                            scenes.append(scene_desc)
            
            self.logger.info(f"提取到的场景数量: {len(scenes)}, 场景: {scenes}")
            
            # 降级处理：如果场景提取失败，使用预定义的经典场景
            # "乌鸦喝水"是一个经过验证的、适合连续图像生成的经典故事
            # 具有清晰的视觉转换和逻辑连贯性，适合作为降级方案
            if len(scenes) < scene_count:
                self.logger.warning("场景提取失败，使用默认乌鸦喝水场景")
                default_scenes = [
                    "乌鸦在炎热的天空中飞行寻找水源",
                    "乌鸦发现瓶子里有水但够不到",
                    "乌鸦聪明地往瓶子里投放石子",
                    "水位上升，乌鸦成功喝到水"
                ]
                scenes = default_scenes[:scene_count]
            
            return scenes[:scene_count]
            
        except Exception as e:
            self.logger.error(f"故事分解失败: {str(e)}")
            # 异常情况下的最终降级：使用经典的乌鸦喝水场景
            # 这个场景序列经过充分测试，能够确保连续图像生成的成功率
            default_scenes = [
                "乌鸦在炎热的天空中飞行寻找水源",
                "乌鸦发现瓶子里有水但够不到", 
                "乌鸦聪明地往瓶子里投放石子",
                "水位上升，乌鸦成功喝到水"
            ]
            return default_scenes[:scene_count]
    
    async def _generate_consistent_prompt(self, base_scene: str, current_scene: str, style: str, is_first: bool = False) -> str:
        """
        生成保持视觉一致性的图像提示词
        
        为连续图像生成优化的提示词策略，确保角色、风格、色调的一致性：
        1. 第一张图：建立基础视觉风格和角色设定
        2. 后续图片：在保持一致性的基础上展现动作变化
        
        采用简化策略避免过度复杂的LLM生成，直接组合关键元素。
        
        Args:
            base_scene: 基础场景描述（用于建立一致性参考）
            current_scene: 当前场景的具体描述
            style: 用户指定的风格要求
            is_first: 是否为序列中的第一张图像
            
        Returns:
            str: 优化后的图像生成提示词
        """
        # 为连续图像生成采用简化的提示词策略
        if is_first:
            # 第一张图：建立基础风格和视觉设定
            prompt = f"{current_scene}, {style}, 卡通连环画风格, 高质量, 详细, 清晰"
        else:
            # 后续图片：明确要求保持一致性，避免风格漂移
            prompt = f"{current_scene}, 与第一幅图保持相同的角色和环境风格, {style}, 卡通连环画风格, 高质量, 详细, 清晰"
        
        return prompt
    
    def _generate_edit_prompt(self, base_scene: str, target_scene: str, style: str) -> str:
        """
        生成图像编辑的提示词
        
        为连续图像生成创建专门的编辑指令，确保角色和风格的一致性
        同时实现场景的自然过渡。
        
        Args:
            base_scene: 基础场景描述
            target_scene: 目标场景描述
            style: 风格要求
            
        Returns:
            str: 图像编辑的提示词
        """
        edit_prompt = f"""保持图像中的主要角色和整体环境风格不变，将场景从 "{base_scene}" 改变为 "{target_scene}"。
        
要求：
1. 保持主要角色的外观、颜色和大小一致
2. 保持整体色调和艺术风格（{style}）
3. 只改变角色的动作、姿态和场景中的具体物品
4. 确保场景过渡自然合理
5. 保持连环画的清晰度和可读性

具体变化：{target_scene}"""
        
        return edit_prompt
    
    async def _handle_single_generation(self, task_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理单张图像生成任务
        
        执行独立的单张图像生成，不涉及连续性和一致性要求。
        适用于：海报、插图、独立的艺术作品等场景。
        
        处理流程：
        1. 提取任务描述和风格要求
        2. 组合生成提示词
        3. 调用底层图像生成API
        4. 提取并返回图像URL
        
        Args:
            task_analysis: 包含任务类型、描述、风格要求等信息的分析结果
            
        Returns:
            Dict包含：
            - status: 生成状态（success/error）
            - task_type: 任务类型标识
            - message: 状态描述信息
            - image_urls: 生成的图像URL列表
            - prompt_used: 实际使用的提示词
        """
        try:
            # 构建图像生成提示词
            prompt = task_analysis['description']
            if task_analysis.get('style_requirements'):
                prompt += f", {task_analysis['style_requirements']}"
            
            # 调用底层图像生成API
            result = self.image_generator.generate_image(
                prompt=prompt,
                n=1,  # 单张生成
                prompt_extend=True  # 启用提示词扩展以获得更好效果
            )
            
            # 处理生成结果
            if result["status"] == "success":
                urls = self.image_generator.extract_image_urls(result)
                return {
                    "status": "success",
                    "task_type": "single_generation",
                    "message": "图像生成成功！",
                    "image_urls": urls,
                    "prompt_used": prompt
                }
            else:
                return {
                    "status": "error",
                    "message": f"图像生成失败: {result.get('error', '未知错误')}"
                }
                
        except Exception as e:
            self.logger.error(f"单张图像生成失败: {str(e)}")
            return {
                "status": "error",
                "message": f"生成失败: {str(e)}"
            }
    
    async def _handle_sequential_generation(self, task_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理连续图像生成任务
        
        生成多张保持角色和风格一致性的连贯图像。
        适用于：故事情节、教程步骤、连环画等需要连贯性的场景。
        
        核心技术：
        1. 故事分解：将完整故事拆分为多个简洁的视觉场景
        2. 一致性策略：使用图像编辑技术而非独立生成确保一致性
        3. 基准建立：第一张图建立视觉基准（角色外观、环境风格）
        4. 渐进编辑：后续图像通过编辑第一张图像生成，保持一致性
        
        Args:
            task_analysis: 包含故事描述、场景数量、风格要求的分析结果
            
        Returns:
            Dict包含：
            - status: 生成状态
            - task_type: 任务类型标识
            - message: 状态描述
            - image_urls: 所有生成图像的URL列表
            - scenes: 分解后的场景描述列表
            - prompts_used: 使用的提示词列表
            - total_scenes: 总场景数
            - generation_method: 生成方法标识
        """
        try:
            # 提取任务参数
            story_description = task_analysis['description']
            scene_count = task_analysis.get('scene_count', 4)
            style = task_analysis.get('style_requirements', '卡通风格，色彩鲜艳')
            
            # 第一步：智能分解故事为多个连贯场景
            scenes = await self._decompose_story(story_description, scene_count)
            
            # 初始化结果收集器
            images = []  # 所有生成的图像URL
            prompts_used = []  # 记录使用的提示词
            
            # 第二步：生成第一张图像作为视觉基准
            # 这张图像将建立整个序列的角色外观和环境风格
            first_prompt = await self._generate_consistent_prompt(
                scenes[0], scenes[0], style, is_first=True
            )
            
            first_result = self.image_generator.generate_image(
                prompt=first_prompt,
                n=1,
                prompt_extend=True
            )
            
            # 检查基准图像生成是否成功
            if first_result["status"] != "success":
                return {
                    "status": "error",
                    "message": f"第一张图像生成失败: {first_result.get('error', '未知错误')}"
                }
            
            # 保存第一张图像作为基准
            first_urls = self.image_generator.extract_image_urls(first_result)
            images.extend(first_urls)
            prompts_used.append(first_prompt)
            
            # 第三步：通过图像编辑生成后续图像，确保连贯性
            # 这是保持一致性的关键：编辑而非重新生成
            current_image_url = first_urls[0] if first_urls else None
            
            # 逐个处理后续场景，通过编辑实现场景转换
            for i, scene in enumerate(scenes[1:], 1):
                if not current_image_url:
                    self.logger.error(f"第{i+1}张图像生成失败：缺少基础图像URL")
                    break
                
                # 生成专门的编辑提示词，确保从基准场景到目标场景的自然转换
                edit_prompt = self._generate_edit_prompt(scenes[0], scene, style)
                self.logger.info(f"生成第{i+1}张图像的编辑提示词: {edit_prompt}")
                
                # 调用图像编辑API，在保持主体的基础上改变场景
                result = self.image_generator.edit_image(
                    image_url=current_image_url,  # 基础图像
                    edit_prompt=edit_prompt,      # 编辑指令
                    model="qwen-image-edit",      # 使用专门的编辑模型
                    watermark=True
                )
                
                # 处理编辑结果
                if result["status"] == "success":
                    urls = self.image_generator.extract_image_urls(result)
                    if urls:
                        images.extend(urls)
                        prompts_used.append(edit_prompt)
                        current_image_url = urls[0]  # 更新当前图像URL用于下一次编辑
                        self.logger.info(f"第{i+1}张图像编辑成功")
                    else:
                        self.logger.warning(f"第{i+1}张图像编辑返回空URL")
                else:
                    self.logger.warning(f"第{i+1}张图像编辑失败: {result.get('error', '未知错误')}")
                    # 继续处理其他图像，使用原始基准图像
            
            return {
                "status": "success",
                "task_type": "sequential_generation",
                "message": f"连续图像生成完成！基于第一张图编辑生成了{len(images)}张连贯图片",
                "image_urls": images,
                "scenes": scenes,
                "prompts_used": prompts_used,
                "total_scenes": len(scenes),
                "generation_method": "image_editing_based"
            }
            
        except Exception as e:
            self.logger.error(f"连续图像生成失败: {str(e)}")
            return {
                "status": "error",
                "message": f"连续生成失败: {str(e)}"
            }
    
    async def _handle_image_edit(self, task_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理图像编辑
        
        Args:
            task_analysis: 任务分析结果
            
        Returns:
            编辑结果
        """
        try:
            # 从task_analysis中获取输入数据
            original_input = task_analysis.get('original_input', '')
            
            # 提取图像URL
            image_url = self._extract_image_url_from_input(original_input)
            if not image_url:
                return {
                    "status": "error",
                    "message": "未能从输入中提取到图像URL"
                }
            
            # 提取编辑指令
            edit_instruction = task_analysis.get('special_requirements', task_analysis.get('description', ''))
            
            self.logger.info(f"提取的图像URL: {image_url}")
            self.logger.info(f"编辑指令: {edit_instruction}")
            
            # 调用图像编辑功能
            edit_result = self.image_generator.edit_image(
                image_url=image_url,
                edit_prompt=edit_instruction
            )
            
            if edit_result and edit_result.get("status") == "success":
                # 使用ImageGenerator的extract_image_urls方法提取图片URL
                image_urls = self.image_generator.extract_image_urls(edit_result)
                
                return {
                    "status": "success",
                    "message": "图像编辑成功！",
                    "task_type": "image_edit",
                    "image_urls": image_urls,
                    "original_image": image_url,
                    "edit_instruction": edit_instruction
                }
            else:
                return {
                    "status": "error",
                    "message": f"图像编辑失败: {edit_result.get('error', '未知错误')}"
                }
                
        except Exception as e:
            self.logger.error(f"图像编辑处理失败: {str(e)}")
            return {
                "status": "error",
                "message": f"图像编辑处理失败: {str(e)}"
            }
    
    def _extract_image_url_from_input(self, input_text: str) -> Optional[str]:
        """
        从输入文本中提取图像URL
        
        Args:
            input_text: 输入文本
            
        Returns:
            提取的图像URL，如果没找到则返回None
        """
        try:
            # 解析JSON格式的输入
            if input_text.strip().startswith('{'):
                import json
                input_data = json.loads(input_text)
                
                # 从image字段中提取URL
                image_content = input_data.get('image', '')
                
                # 查找URL模式
                url_patterns = [
                    r'https://[^\s\)]+\.png[^\s\)]*',
                    r'https://[^\s\)]+\.jpg[^\s\)]*', 
                    r'https://[^\s\)]+\.jpeg[^\s\)]*',
                    r'图片地址:\s*(https://[^\s\n]+)',
                    r'\[生成的图片\d*\]\((https://[^\)]+)\)'
                ]
                
                for pattern in url_patterns:
                    matches = re.findall(pattern, image_content)
                    if matches:
                        # 返回第一个匹配的URL
                        url = matches[0] if isinstance(matches[0], str) else matches[0][0]
                        self.logger.info(f"使用正则表达式 {pattern} 提取到URL: {url}")
                        return url.strip()
            
            # 如果JSON解析失败，直接在文本中查找URL
            url_patterns = [
                r'https://[^\s\)]+\.png[^\s\)]*',
                r'https://[^\s\)]+\.jpg[^\s\)]*',
                r'https://[^\s\)]+\.jpeg[^\s\)]*'
            ]
            
            for pattern in url_patterns:
                matches = re.findall(pattern, input_text)
                if matches:
                    url = matches[0]
                    self.logger.info(f"直接从文本提取到URL: {url}")
                    return url.strip()
                    
            return None
            
        except Exception as e:
            self.logger.error(f"提取图像URL失败: {str(e)}")
            return None
    
    async def process_task(self, task_input: str) -> Dict[str, Any]:
        """
        处理图像任务的主入口
        
        Args:
            task_input: 任务输入描述
            
        Returns:
            处理结果
        """
        try:
            self.logger.info(f"开始处理图像任务: 任务描述: {task_input[:100]}{'...' if len(task_input) > 100 else ''}")
            self.logger.info(f"输入数据: {task_input}")
            
            # 分析任务类型
            task_analysis = await self._analyze_task(task_input)
            self.logger.info(f"任务分析结果: {task_analysis}")
            
            # 根据任务类型处理
            if task_analysis['task_type'] == 'sequential_generation':
                result = await self._handle_sequential_generation(task_analysis)
            elif task_analysis['task_type'] == 'single_generation':
                result = await self._handle_single_generation(task_analysis)
            elif task_analysis['task_type'] == 'image_edit':
                result = await self._handle_image_edit(task_analysis)
            else:
                result = {
                    "status": "error",
                    "message": f"未知的任务类型: {task_analysis['task_type']}"
                }
            
            # 记录到历史
            task_record = {
                "task_input": task_input,
                "task_analysis": task_analysis,
                "result": result,
                "timestamp": datetime.now().isoformat()
            }
            self.generation_history.append(task_record)
            
            return result
            
        except Exception as e:
            self.logger.error(f"处理图像任务失败: {str(e)}")
            return {
                "status": "error",
                "task_input": task_input,
                "result": f"处理失败: {str(e)}",
                "timestamp": datetime.now().isoformat()
            }
    
    def get_generation_history(self) -> List[Dict[str, Any]]:
        """
        获取图像生成历史
        
        Returns:
            生成历史列表
        """
        return self.generation_history
    
    def clear_history(self):
        """
        清空生成历史
        """
        self.generation_history.clear()
        self.logger.info("图像生成历史已清空")
