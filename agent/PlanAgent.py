"""
PlanAgent - 规划智能体
============================

这是Agentic RAG系统中的核心规划组件，采用Orchestrator-workers架构模式。

主要职责：
1. 分析用户问题的复杂度、类型和需求
2. 将复杂问题分解为可执行的子任务
3. 选择合适的专门Agent执行各项任务
4. 设计任务执行顺序和依赖关系
5. 管理变量引用和数据传递
6. 提供任务相关性评估和重规划能力

支持的Agent类型：
- MemoryAgent: 短期/长期内存管理，上下文保存和检索
- GeoAgent: 地理信息、天气、POI查询
- GenImgAgent: 图像生成和编辑
- WebSearchAgent: 通用网络搜索和信息检索

核心设计原则：
- 一次性解决简单任务，避免不必要的拆分
- 复杂任务按依赖关系有序执行
- 严格的变量引用格式控制
- 智能的Agent选择和任务分配
"""

import json
import re
from typing import List, Dict, Any, Optional, Tuple
from enum import Enum
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from util.llmcallqianwen import llm_call


class AgentType(Enum):
    """
    Agent类型枚举
    
    定义系统中可用的专门化Agent类型：
    - MEMORY: 内存管理Agent，处理上下文保存和检索
    - WEB_SEARCH: 网络搜索Agent，处理通用信息检索
    - GEO: 地理信息Agent，处理天气、位置、路径规划
    - GEN_IMG: 图像生成Agent，处理图像生成和编辑任务
    """
    MEMORY = "memory"
    WEB_SEARCH = "web_search"
    GEO = "geo"
    GEN_IMG = "gen_img"


class PlanStep:
    """
    规划步骤类 - 表示执行计划中的单个步骤
    
    包含执行步骤的完整信息：
    - 步骤标识和描述
    - 指定的Agent类型
    - 输入数据和依赖关系
    - 执行状态和输出结果
    """
    def __init__(self, step_id: str, agent_type: AgentType, task_description: str, 
                 input_data: str = "", dependencies: List[str] = None):
        """
        初始化规划步骤
        
        Args:
            step_id: 步骤唯一标识符，格式如"S1", "S2"等
            agent_type: 执行此步骤的Agent类型
            task_description: 任务描述，告诉Agent要做什么
            input_data: 输入数据，可能包含变量引用
            dependencies: 依赖的其他步骤ID列表
        """
        self.step_id = step_id
        self.agent_type = agent_type
        self.task_description = task_description
        self.input_data = input_data
        self.dependencies = dependencies or []
        self.output = ""  # 步骤执行的输出结果
        self.status = "pending"  # 步骤状态：pending, running, completed, failed
        
    def to_dict(self) -> Dict[str, Any]:
        """
        将步骤对象转换为字典格式
        
        Returns:
            包含步骤所有信息的字典，用于序列化和传输
        """
        return {
            "step_id": self.step_id,
            "agent_type": self.agent_type.value,
            "task_description": self.task_description,
            "input_data": self.input_data,
            "dependencies": self.dependencies,
            "output": self.output,
            "status": self.status
        }


class ExecutionPlan:
    """
    执行计划类 - 包含完整的任务执行规划
    
    管理多个执行步骤的有序集合：
    - 维护步骤列表和执行顺序
    - 跟踪当前执行进度
    - 提供计划状态管理
    """
    def __init__(self, steps: List[PlanStep]):
        """
        初始化执行计划
        
        Args:
            steps: 规划步骤列表，按执行顺序排列
        """
        self.steps = steps
        self.current_step = 0  # 当前执行到的步骤索引
        self.status = "pending"  # 计划状态：pending, running, completed, failed
        
    def to_dict(self) -> Dict[str, Any]:
        """
        将执行计划转换为字典格式
        
        Returns:
            包含完整执行计划信息的字典
        """
        return {
            "steps": [step.to_dict() for step in self.steps],
            "current_step": self.current_step,
            "status": self.status
        }


class PlanAgent:
    """
    规划Agent - 系统的核心规划组件
    
    作为Orchestrator-workers架构中的Orchestrator，负责：
    1. 分析用户问题，理解需求类型和复杂度
    2. 选择合适的专门化Agent执行任务
    3. 设计执行步骤和依赖关系
    4. 管理变量引用和数据流
    5. 提供执行状态管理和监控
    
    核心方法：
    - analyze_user_question: 问题分析和初步规划
    - create_execution_plan: 创建详细执行计划
    - update_step_status: 更新步骤执行状态

    """
    
    def __init__(self):
        """
        初始化规划Agent
        """
        self.current_plan: Optional[ExecutionPlan] = None  # 当前执行的计划
        
    def analyze_user_question(self, user_question: str) -> Dict[str, Any]:
        """
        分析用户问题，确定需要哪些Agent参与以及执行顺序
        
        Args:
            user_question: 用户提出的问题
            
        Returns:
            包含分析结果和执行计划的字典
        """
        
        # 构建提示词
        prompt = f"""
你是一个规划型智能体（Planning Agent）。
核心职责：
    • 分析用户问题的复杂度、类型与需求
    • 选择合适助手执行任务
    • 将复杂问题拆分为子任务，设计执行顺序与依赖关系

可用助手：
    • MemoryAgent：
      - 保存/回溯任务、上下文、偏好，管理短期内存和长期内存。
      - 任何时候用户明确提供地理位置、住址、出行方式、兴趣偏好、图片风格、常用参数等信息，都必须调用 MemoryAgent 存储。
      - 用户请求缺少关键信息、使用模糊指代（如"上次""再来一个"）、涉及长期偏好（如默认城市/交通方式/风格）、或任务需复用历史结果时，必须调用 MemoryAgent 查询。
      - 特别重要：当用户使用引用词语（如"上图""刚刚的图片""前面的""那张图""上面的图片""最后一个"等）时，必须先调用 MemoryAgent 查询对应的内容，然后再进行后续操作。
      - 关键例外：如果用户问题中包含"[引用上下文已解析]"标记，说明引用已经被系统预处理，不需要再调用 MemoryAgent 查询。
	• GeoAgent：
	  - 唯一指定用于天气、地理位置、路径规划、POI 查询/对比。
	  - 涉及天气/位置/POI → 必须调用，不得改用 WebSearchAgent。
	  - 输出格式：返回包含result字段的JSON，result字段包含完整的文本描述。
	• GenImgAgent：
	  - 专门用于图像生成和编辑任务。
	  - 支持单张图像生成、连续图像生成（如故事情节、教程步骤）、图像编辑。
	  - 涉及"生成图片""画图""制作图像""图像编辑""连环画""故事配图"等需求时必须调用。
	• WebSearchAgent：
	  - 通用外部检索（新闻、百科、资料核验等）。
	  - 仅当问题不属于 MemoryAgent、GeoAgent 或 GenImgAgent 时调用。

变量引用规则：
    • 引用前一步骤的输出时，使用格式：{{步骤ID.result}}
    • 例如：引用S1步骤的输出使用{{S1.result}}，引用S2步骤的输出使用{{S2.result}}
    • 所有Agent的输出都包含在result字段中，直接引用{{步骤ID.result}}即可获取完整内容
    • 重要：只能使用{{步骤ID.result}}
    • 强制要求：当后续步骤需要前一步骤的结果时，inputs中必须包含{{步骤ID.result}}变量引用
    • 严格禁止：绝不能使用{{S1.result.image_data}}、{{S1.result.url}}、{{S1.result.content}}等嵌套格式

选择与拆分原则：
	1. 简单问题 → 单一助手解决。
	2. 复杂问题 → 拆分子任务，设定依赖。
	3. 引用场景处理 → 如果用户使用"上图""刚才的""前面的""那个"等引用词，必须首先调用MemoryAgent查询引用内容，然后根据查询结果进行后续操作。
	4. 天气/地理/POI → GeoAgent。
	5. 图像生成/编辑/故事配图 → GenImgAgent。
	6. 最新信息/资料核验 → WebSearchAgent。
	7. 上下文记忆/用户偏好/历史结果 → MemoryAgent。
	8. 避免冗余：能一次解决不拆分；同类信息不重复检索。
	9. 冲突：地理/天气任务优先用 GeoAgent，图像任务优先用 GenImgAgent，再结合其他Agent。

示例场景 - 处理引用：
用户问题："把上面的图片人物手里的伞去掉"
正确规划：
步骤1: 使用MemoryAgent查询"上面的图片"指代的具体图片内容
步骤2: 使用GenImgAgent对查询到的图片进行编辑，去掉人物手里的伞

示例场景 - 变量引用：
用户问题："查询北京天气，然后生成一张反映天气的图片"
正确规划：
{{
  "steps": [
    {{
      "id": "S1",
      "assistant": "GeoAgent",
      "action": "查询北京天气",
      "inputs": {{"location": "北京"}}
    }},
    {{
      "id": "S2", 
      "assistant": "GenImgAgent",
      "action": "根据天气信息生成图片",
      "inputs": {{"prompt": "根据天气信息生成图片", "weather_info": "{{S1.result}}"}}
    }}
  ]
}}

错误示例（严格禁止）：
- "inputs": {{"image_data": "{{S1.result.image_data}}"}}  ❌ 错误！
- "inputs": {{"url": "{{S1.result.url}}"}}  ❌ 错误！
- "inputs": {{"content": "{{S1.result.content}}"}}  ❌ 错误！

正确示例：
- "inputs": {{"image_data": "{{S1.result}}"}}  ✅ 正确！

用户问题：{user_question}

请输出JSON格式规划结果：

{{
  "execution_plan": {{
    "steps": [
      {{
        "id": "S1",
        "assistant": "MemoryAgent|GeoAgent|GenImgAgent|WebSearchAgent",
        "action": "具体动作",
        "inputs": {{"key": "value"}},
        "expect": {{
          "output_type": "text|json|list",
          "export_key": "result_key",
          "schema_hint": "输出格式描述"
        }},
        "validation": ["验收标准1","验收标准2"],
        "deps": [],
        "retry": {{"max_attempts": 1,"backoff": "none"}},
        "on_error": "fail|continue|fallback",
        "fallback": null,
        "persist": {{
          "use_memory_agent": true|false,
          "keys": [],
          "data": {{}}
        }}
      }}
    ],
    "post_process": {{
      "synthesis": {{
        "template": "最终答案模板",
        "inputs": ["S1.result"]
      }}
    }}
  }}
}}
        """
        
        try:
            # 调用LLM进行问题分析和规划
            print(f"正在调用LLM进行问题分析，提示词长度: {len(prompt)}")
            response = llm_call(prompt)
            print(f"LLM响应: {response[:200]}..." if len(response) > 200 else f"LLM响应: {response}")
            
            # 处理空响应情况
            if not response or response.strip() == "":
                print("LLM返回空响应，直接返回空结果")
                return {
                    "direct_response": "抱歉，我无法处理您的问题，请稍后重试。",
                    "execution_plan": {
                        "steps": []
                    }
                }
            
            # 清理可能包含Markdown代码块的响应
            json_content = response.strip()
            if json_content.startswith('```json'):
                json_content = json_content[7:]  # 移除开头的```json
                if json_content.endswith('```'):
                    json_content = json_content[:-3]  # 移除结尾的```
                json_content = json_content.strip()
            
            # 解析JSON响应
            result = json.loads(json_content)
            
            return result
        except json.JSONDecodeError as e:
            # JSON解析失败处理
            print(f"JSON解析错误: {e}")
            print(f"原始响应内容: {response}")
            return {
                "direct_response": response,  # 返回原始响应供用户参考
                "execution_plan": {
                    "steps": []
                },
                "parse_error": f"JSON解析失败: {str(e)}"
            }
        except Exception as e:
            # 其他系统错误处理
            print(f"系统错误: {e}")
            return {
                "direct_response": f"抱歉，系统遇到错误：{str(e)}，请稍后重试。",
                "execution_plan": {
                    "steps": []
                },
                "system_error": str(e)
            }
    
    def create_execution_plan(self, user_question: str, analysis_result: Dict[str, Any] = None) -> ExecutionPlan:
        """
        基于分析结果创建详细的执行计划
        
        将LLM的分析结果转换为可执行的PlanStep对象序列：
        1. 解析LLM返回的执行计划JSON
        2. 将assistant类型映射到AgentType枚举
        3. 创建PlanStep对象并设置依赖关系
        4. 构建完整的ExecutionPlan对象
        
        Args:
            user_question: 用户问题，用于重新分析（如果需要）
            analysis_result: LLM分析结果，如果未提供则重新分析
            
        Returns:
            可执行的ExecutionPlan对象
        """
        # 获取分析结果（重新分析或使用提供的结果）
        if analysis_result is None:
            result = self.analyze_user_question(user_question)
        else:
            result = analysis_result
        
        # 提取执行计划数据
        execution_plan = result.get("execution_plan", {})
        steps_data = execution_plan.get("steps", [])
        
        # 将LLM返回的步骤数据转换为PlanStep对象
        steps = []
        for step_data in steps_data:
            # 映射LLM使用的assistant名称到内部AgentType枚举
            assistant_type = step_data.get("assistant", "")
            agent_type = None
            
            if assistant_type == "MemoryAgent":
                agent_type = AgentType.MEMORY
            elif assistant_type == "WebSearchAgent":
                agent_type = AgentType.WEB_SEARCH
            elif assistant_type == "GeoAgent":
                agent_type = AgentType.GEO
            elif assistant_type == "GenImgAgent":
                agent_type = AgentType.GEN_IMG
            else:
                # 跳过无效或未识别的assistant类型
                print(f"跳过无效的assistant类型: {assistant_type}")
                continue
            
            # 创建执行步骤对象
            step = PlanStep(
                step_id=step_data.get("id", f"step_{len(steps)+1}"),  # 步骤ID，如S1, S2等
                agent_type=agent_type,                                # 执行Agent类型
                task_description=step_data.get("action", ""),         # 任务描述
                input_data=str(step_data.get("inputs", "")),          # 输入数据（可能包含变量引用）
                dependencies=step_data.get("deps", [])               # 依赖的其他步骤
            )
            steps.append(step)
        
        # 创建并保存执行计划
        plan = ExecutionPlan(steps)
        self.current_plan = plan
        return plan
    
    def update_step_status(self, step_id: str, status: str, output: str = "") -> bool:
        """
        更新指定步骤的执行状态
        
        在步骤执行过程中实时更新状态和输出结果：
        - 支持状态跟踪：pending -> running -> completed/failed
        - 保存步骤执行的输出结果
        - 提供执行进度监控
        
        Args:
            step_id: 要更新的步骤ID（如"S1", "S2"等）
            status: 新的执行状态
                   - "pending": 等待执行
                   - "running": 正在执行 
                   - "completed": 执行完成
                   - "failed": 执行失败
            output: 步骤执行的输出结果（可选）
            
        Returns:
            bool: 是否成功更新状态
        """
        try:
            # 检查是否有当前执行计划
            if not self.current_plan:
                return False
            
            # 查找并更新对应的步骤
            for step in self.current_plan.steps:
                if step.step_id == step_id:
                    step.status = status
                    if output:
                        step.output = output
                    return True
            
            # 未找到对应步骤
            return False
        except Exception as e:
            print(f"更新步骤状态失败: {e}")
            return False

