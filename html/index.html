<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能问答助手</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100vh;
            overflow: hidden;
        }

        .container {
            display: flex;
            height: 100vh;
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            box-shadow: 0 0 30px rgba(0,0,0,0.1);
        }

        /* 左侧聊天区域 */
        .chat-section {
            flex: 2;
            display: flex;
            flex-direction: column;
            border-right: 1px solid #e0e0e0;
        }

        .chat-header {
            background: #4a90e2;
            color: white;
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .chat-header h1 {
            font-size: 24px;
            font-weight: 600;
        }

        .chat-messages {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
            background: #f8f9fa;
        }

        .message {
            margin-bottom: 20px;
            animation: fadeIn 0.3s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .message.user {
            text-align: right;
        }

        .message.assistant {
            text-align: left;
        }

        .message-content {
            display: inline-block;
            max-width: 80%;
            padding: 15px 20px;
            border-radius: 20px;
            word-wrap: break-word;
            line-height: 1.5;
        }

        .message.user .message-content {
            background: #4a90e2;
            color: white;
            border-bottom-right-radius: 5px;
        }

        .message.assistant .message-content {
            background: white;
            color: #333;
            border: 1px solid #e0e0e0;
            border-bottom-left-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .chat-input {
            padding: 20px;
            background: white;
            border-top: 1px solid #e0e0e0;
        }

        .input-group {
            display: flex;
            gap: 10px;
        }

        .input-group input {
            flex: 1;
            padding: 15px 20px;
            border: 2px solid #e0e0e0;
            border-radius: 25px;
            font-size: 16px;
            outline: none;
            transition: border-color 0.3s;
        }

        .input-group input:focus {
            border-color: #4a90e2;
        }

        .input-group button {
            padding: 15px 25px;
            background: #4a90e2;
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: background 0.3s;
        }

        .input-group button:hover:not(:disabled) {
            background: #357abd;
        }

        .input-group button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }

        /* 右侧执行详情区域 */
        .details-section {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: #f5f7fa;
        }

        .details-header {
            background: #2c3e50;
            color: white;
            padding: 20px;
            text-align: center;
        }

        .details-header h2 {
            font-size: 18px;
            font-weight: 600;
        }

        .details-content {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
        }

        .detail-item {
            background: white;
            margin-bottom: 15px;
            border-radius: 10px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            overflow: hidden;
            animation: slideIn 0.3s ease-out;
        }

        @keyframes slideIn {
            from { opacity: 0; transform: translateX(20px); }
            to { opacity: 1; transform: translateX(0); }
        }

        .detail-header {
            background: #34495e;
            color: white;
            padding: 12px 15px;
            font-weight: 600;
            font-size: 14px;
        }

        .detail-body {
            padding: 15px;
            font-size: 13px;
            line-height: 1.6;
            color: #555;
        }

        .detail-body pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
            margin: 10px 0;
            border-left: 4px solid #4a90e2;
        }

        .status-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-pending {
            background: #f39c12;
            animation: pulse 1.5s infinite;
        }

        .status-running {
            background: #3498db;
            animation: pulse 1.5s infinite;
        }

        .status-completed {
            background: #27ae60;
        }

        .status-error {
            background: #e74c3c;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .typing-indicator {
            display: none;
            padding: 15px 20px;
            color: #666;
            font-style: italic;
        }

        .typing-indicator.show {
            display: block;
        }

        .typing-dots {
            display: inline-block;
        }

        .typing-dots::after {
            content: '';
            animation: typing 1.5s infinite;
        }

        @keyframes typing {
            0% { content: ''; }
            25% { content: '.'; }
            50% { content: '..'; }
            75% { content: '...'; }
            100% { content: ''; }
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .container {
                flex-direction: column;
            }
            
            .chat-section {
                flex: 1;
                border-right: none;
                border-bottom: 1px solid #e0e0e0;
            }
            
            .details-section {
                flex: 0 0 300px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 左侧聊天区域 -->
        <div class="chat-section">
            <div class="chat-header">
                <h1>🤖 智能问答助手</h1>
                <p>基于多Agent架构的智能问答系统</p>
            </div>
            
            <div class="chat-messages" id="chatMessages">
                <div class="message assistant">
                    <div class="message-content">
                        👋 您好！我是智能问答助手，可以帮您解答各种问题，包括地理位置查询、网络搜索、记忆存储等。请输入您的问题！
                    </div>
                </div>
            </div>
            
            <div class="typing-indicator" id="typingIndicator">
                <span class="typing-dots">AI正在思考中</span>
            </div>
            
            <div class="chat-input">
                <div class="input-group">
                    <input type="text" id="messageInput" placeholder="请输入您的问题..." maxlength="500">
                    <button id="sendButton" onclick="sendMessage()">发送</button>
                </div>
            </div>
        </div>
        
        <!-- 右侧执行详情区域 -->
        <div class="details-section">
            <div class="details-header">
                <h2>📊 执行详情</h2>
                <p>实时显示AI的思考和执行过程</p>
            </div>
            
            <div class="details-content" id="detailsContent">
                <div class="detail-item">
                    <div class="detail-header">
                        💡 使用说明
                    </div>
                    <div class="detail-body">
                        <p>这里将实时显示AI的执行过程，包括：</p>
                        <ul style="margin: 10px 0; padding-left: 20px;">
                            <li>📋 执行计划分析</li>
                            <li>⚙️ 各步骤执行结果</li>
                            <li>🎯 最终答案生成</li>
                            <li>📈 执行摘要统计</li>
                            <li>✅ 相关性检查结果</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let eventSource = null;
        let currentMessageId = null;

        // 发送消息
        function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();
            
            if (!message) return;
            
            // 添加用户消息到聊天区域
            addMessage('user', message);
            
            // 清空输入框并禁用发送按钮
            input.value = '';
            toggleSendButton(false);
            
            // 显示打字指示器
            showTypingIndicator(true);
            
            // 清空执行详情
            clearDetails();
            
            // 发送请求到后端
            sendToBackend(message);
        }

        // 发送到后端
        function sendToBackend(message) {
            // 关闭之前的连接
            if (eventSource) {
                eventSource.close();
            }

            // 创建新的SSE连接
            const encodedMessage = encodeURIComponent(message);
            eventSource = new EventSource(`/api/chat?message=${encodedMessage}`);
            
            let assistantMessageElement = null;
            
            eventSource.onmessage = function(event) {
                try {
                    const data = JSON.parse(event.data);
                    
                    switch(data.type) {
                        case 'analysis':
                            addDetailItem('📋 问题分析', data.content, 'running');
                            break;
                            
                        case 'plan':
                            addDetailItem('🎯 执行计划', data.content, 'running');
                            break;
                            
                        case 'step_start':
                            addDetailItem(`⚙️ 步骤${data.step_id}`, `开始执行: ${data.description}`, 'running');
                            break;
                            
                        case 'step_result':
                            updateDetailItem(`⚙️ 步骤${data.step_id}`, data.content, 'completed');
                            break;
                            
                        case 'final_answer':
                            showTypingIndicator(false);
                            const processedMessage = processMessageContent(data.content);
                            assistantMessageElement = addMessage('assistant', processedMessage.content, processedMessage.isHtml);
                            if (!processedMessage.isHtml) {
                                // 只有纯文本才使用流式显示
                                assistantMessageElement.querySelector('.message-content').textContent = '';
                                streamText(assistantMessageElement.querySelector('.message-content'), data.content);
                            }
                            break;
                            
                        case 'summary':
                            addDetailItem('📈 执行摘要', data.content, 'completed');
                            break;
                            
                        case 'relevance':
                            addDetailItem('✅ 相关性检查', data.content, 'completed');
                            break;
                            
                        case 'error':
                            showTypingIndicator(false);
                            addMessage('assistant', `❌ 抱歉，处理您的问题时出现了错误：${data.content}`);
                            addDetailItem('❌ 错误信息', data.content, 'error');
                            break;
                            
                        case 'complete':
                            toggleSendButton(true);
                            // 关闭SSE连接
                            if (eventSource) {
                                eventSource.close();
                                eventSource = null;
                            }
                            break;
                    }
                } catch (e) {
                    console.error('解析SSE数据失败:', e);
                }
            };
            
            eventSource.onerror = function(event) {
                console.error('SSE连接错误:', event);
                showTypingIndicator(false);
                toggleSendButton(true);
                
                // 检查连接状态，只有在真正连接失败时才显示错误消息
                if (eventSource.readyState === EventSource.CLOSED) {
                    addMessage('assistant', '❌ 连接服务器失败，请稍后重试。');
                }
                
                // 关闭连接以避免重复错误
                eventSource.close();
                eventSource = null;
            };
        }

        // 添加消息到聊天区域
        function addMessage(sender, content, isHtml = false) {
            const messagesContainer = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}`;
            
            const contentDiv = document.createElement('div');
            contentDiv.className = 'message-content';
            
            if (isHtml) {
                contentDiv.innerHTML = content;
            } else {
                contentDiv.textContent = content;
            }
            
            messageDiv.appendChild(contentDiv);
            messagesContainer.appendChild(messageDiv);
            
            // 滚动到底部
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
            
            return messageDiv;
        }
        
        // 处理包含图片的消息内容
        function processMessageContent(content) {
            console.log('processMessageContent 收到内容:', content);
            console.log('内容长度:', content.length);
            console.log('内容类型:', typeof content);
            
            // 检查是否包含Markdown格式的图片或直接的图片URL
            const markdownImageRegex = /!\[([^\]]*)\]\(([^)]+)\)/gi;
            // 修复：更宽松的图片URL匹配，支持阿里云OSS等复杂URL格式
            const directImageUrlRegex = /(https?:\/\/[^\s\)]+\.(jpg|jpeg|png|gif|webp|svg)(?:\?[^\s\)]*)?)/gi;
            // 新增：处理反引号包裹的图片URL格式，如: ! `https://...`
            const backtickImageRegex = /!\s*`(https?:\/\/[^`]+\.(jpg|jpeg|png|gif|webp|svg)(?:\?[^`]*)?)`/gi;
            
            let hasImages = false;
            let processedContent = content;
            
            // 创建代理URL的函数
            function createProxyUrl(originalUrl) {
                return `/api/proxy-image?url=${encodeURIComponent(originalUrl)}`;
            }
            
            // 预加载图片的函数，确保图片可以正常加载
            function preloadImage(url) {
                return new Promise((resolve, reject) => {
                    const img = new Image();
                    const timeout = setTimeout(() => {
                        reject(new Error('图片加载超时'));
                    }, 10000); // 10秒超时
                    
                    img.onload = () => {
                        clearTimeout(timeout);
                        resolve(url);
                    };
                    
                    img.onerror = () => {
                        clearTimeout(timeout);
                        reject(new Error('图片加载失败'));
                    };
                    
                    img.src = url;
                });
            }
            
            // 处理反引号包裹的图片URL格式
            const backtickMatches = content.match(backtickImageRegex);
            if (backtickMatches && backtickMatches.length > 0) {
                console.log('发现反引号包裹的图片URL，数量:', backtickMatches.length);
                hasImages = true;
                // 重新创建正则表达式避免test()消耗掉匹配
                const freshBacktickRegex = /!\s*`(https?:\/\/[^`]+\.(jpg|jpeg|png|gif|webp|svg)(?:\?[^`]*)?)`/gi;
                processedContent = processedContent.replace(freshBacktickRegex, (match, url) => {
                    console.log('匹配到反引号图片:', match, 'url:', url);
                    const proxyUrl = createProxyUrl(url);
                    console.log('代理URL:', proxyUrl);
                    const imageId = 'img_' + Math.random().toString(36).substr(2, 9);
                    
                    return `<br><img id="${imageId}" src="${proxyUrl}" alt="生成的图片" 
                        style="max-width: 100%; height: auto; border-radius: 8px; margin: 10px 0; box-shadow: 0 2px 8px rgba(0,0,0,0.1);" 
                        onerror="handleImageError(this, '${proxyUrl.replace(/'/g, "\\'").replace(/"/g, '\\"')}', '${match.replace(/'/g, "\\'").replace(/"/g, '\\"')}')" />
                        <span id="${imageId}_error" style="display:none; color: #999; font-style: italic;">图片加载失败，正在重试...</span><br>`;
                });
            }
            
            // 处理Markdown格式的图片
            const markdownMatches = content.match(markdownImageRegex);
            if (markdownMatches && markdownMatches.length > 0) {
                console.log('发现Markdown格式图片，content:', content);
                console.log('匹配到的图片数量:', markdownMatches.length);
                console.log('匹配到的具体内容:', markdownMatches);
                hasImages = true;
                // 重新创建正则表达式避免test()消耗掉匹配
                const freshMarkdownRegex = /!\[([^\]]*)\]\(([^)]+)\)/gi;
                processedContent = processedContent.replace(freshMarkdownRegex, (match, alt, url) => {
                    console.log('匹配到图片:', match, 'alt:', alt, 'url:', url);
                    const proxyUrl = createProxyUrl(url);
                    console.log('代理URL:', proxyUrl);
                    const imageId = 'img_' + Math.random().toString(36).substr(2, 9);
                    
                    // 增强的错误处理和重试机制
                    return `<br><img id="${imageId}" src="${proxyUrl}" alt="${alt || '生成的图片'}" 
                        style="max-width: 100%; height: auto; border-radius: 8px; margin: 10px 0; box-shadow: 0 2px 8px rgba(0,0,0,0.1);" 
                        onerror="handleImageError(this, '${proxyUrl.replace(/'/g, "\\'").replace(/"/g, '\\"')}', '${url.replace(/'/g, "\\'").replace(/"/g, '\\"')}')" />
                        <span id="${imageId}_error" style="display:none; color: #999; font-style: italic;">图片加载失败，正在重试...</span><br>`;
                });
            }
            
            // 处理直接的图片URL
            const directMatches = processedContent.match(directImageUrlRegex);
            if (directMatches && directMatches.length > 0) {
                console.log('发现直接图片URL，数量:', directMatches.length);
                hasImages = true;
                // 重新创建正则表达式，修复：更宽松的匹配规则
                const freshDirectRegex = /(https?:\/\/[^\s\)]+\.(jpg|jpeg|png|gif|webp|svg)(?:\?[^\s\)]*)?)/gi;
                processedContent = processedContent.replace(freshDirectRegex, (match) => {
                    console.log('匹配到直接图片URL:', match);
                    const proxyUrl = createProxyUrl(match);
                    const imageId = 'img_' + Math.random().toString(36).substr(2, 9);
                    
                    return `<br><img id="${imageId}" src="${proxyUrl}" alt="生成的图片" 
                        style="max-width: 100%; height: auto; border-radius: 8px; margin: 10px 0; box-shadow: 0 2px 8px rgba(0,0,0,0.1);" 
                        onerror="handleImageError(this, '${proxyUrl.replace(/'/g, "\\'").replace(/"/g, '\\"')}', '${match.replace(/'/g, "\\'").replace(/"/g, '\\"')}')" />
                        <span id="${imageId}_error" style="display:none; color: #999; font-style: italic;">图片加载失败，正在重试...</span><br>`;
                });
            }
            
            if (hasImages) {
                console.log('检测到图片，返回HTML格式');
                console.log('处理后的内容:', processedContent);
                return { content: processedContent, isHtml: true };
            }

            console.log('未检测到图片，返回纯文本格式');
            return { content: content, isHtml: false };
        }

        // 图片加载错误处理函数
        function handleImageError(imgElement, proxyUrl, originalUrl) {
            console.error('图片加载失败:', proxyUrl, '原始URL:', originalUrl);
            
            const retryCount = parseInt(imgElement.dataset.retryCount || '0');
            const maxRetries = 3;
            
            if (retryCount < maxRetries) {
                // 显示重试提示
                const errorSpan = imgElement.nextElementSibling;
                if (errorSpan) {
                    errorSpan.style.display = 'inline';
                    errorSpan.textContent = `图片加载失败，正在重试 (${retryCount + 1}/${maxRetries})...`;
                }
                
                // 延迟重试，使用指数退避策略
                const retryDelay = Math.pow(2, retryCount) * 1000; // 1s, 2s, 4s
                
                setTimeout(() => {
                    imgElement.dataset.retryCount = (retryCount + 1).toString();
                    
                    // 尝试直接使用原始URL（可能CORS问题已解决）
                    if (retryCount === 0) {
                        console.log('第一次重试：使用原始URL', originalUrl);
                        imgElement.src = originalUrl;
                    } else {
                        // 重新尝试代理URL，添加时间戳防止缓存
                        const timestamp = Date.now();
                        const newProxyUrl = `${proxyUrl}&t=${timestamp}`;
                        console.log('重试代理URL:', newProxyUrl);
                        imgElement.src = newProxyUrl;
                    }
                }, retryDelay);
            } else {
                // 所有重试都失败了
                imgElement.style.display = 'none';
                const errorSpan = imgElement.nextElementSibling;
                if (errorSpan) {
                    errorSpan.style.display = 'inline';
                    errorSpan.textContent = '图片加载失败';
                    errorSpan.style.color = '#f44336';
                }
                
                // 提供点击重新加载的选项
                const retryButton = document.createElement('button');
                retryButton.textContent = '重新加载';
                retryButton.style.marginLeft = '10px';
                retryButton.style.padding = '2px 8px';
                retryButton.style.fontSize = '12px';
                retryButton.style.cursor = 'pointer';
                retryButton.onclick = () => {
                    imgElement.dataset.retryCount = '0';
                    imgElement.style.display = 'inline';
                    errorSpan.style.display = 'none';
                    retryButton.remove();
                    imgElement.src = proxyUrl;
                };
                errorSpan.appendChild(retryButton);
            }
        }

        // 流式显示文本
        function streamText(element, text) {
            let index = 0;
            const interval = setInterval(() => {
                if (index < text.length) {
                    element.textContent += text[index];
                    index++;
                    // 滚动到底部
                    document.getElementById('chatMessages').scrollTop = document.getElementById('chatMessages').scrollHeight;
                } else {
                    clearInterval(interval);
                }
            }, 20);
        }

        // 添加执行详情项
        function addDetailItem(title, content, status = 'pending') {
            const detailsContainer = document.getElementById('detailsContent');
            
            const itemDiv = document.createElement('div');
            itemDiv.className = 'detail-item';
            itemDiv.id = `detail-${title.replace(/[^a-zA-Z0-9]/g, '')}`;
            
            const headerDiv = document.createElement('div');
            headerDiv.className = 'detail-header';
            headerDiv.innerHTML = `<span class="status-indicator status-${status}"></span>${title}`;
            
            const bodyDiv = document.createElement('div');
            bodyDiv.className = 'detail-body';
            
            if (typeof content === 'object') {
                bodyDiv.innerHTML = `<pre>${JSON.stringify(content, null, 2)}</pre>`;
            } else {
                bodyDiv.textContent = content;
            }
            
            itemDiv.appendChild(headerDiv);
            itemDiv.appendChild(bodyDiv);
            detailsContainer.appendChild(itemDiv);
            
            // 滚动到底部
            detailsContainer.scrollTop = detailsContainer.scrollHeight;
        }

        // 更新执行详情项
        function updateDetailItem(title, content, status) {
            const itemId = `detail-${title.replace(/[^a-zA-Z0-9]/g, '')}`;
            const item = document.getElementById(itemId);
            
            if (item) {
                const statusIndicator = item.querySelector('.status-indicator');
                const bodyDiv = item.querySelector('.detail-body');
                
                statusIndicator.className = `status-indicator status-${status}`;
                
                if (typeof content === 'object') {
                    bodyDiv.innerHTML = `<pre>${JSON.stringify(content, null, 2)}</pre>`;
                } else {
                    bodyDiv.textContent = content;
                }
            }
        }

        // 清空执行详情
        function clearDetails() {
            const detailsContainer = document.getElementById('detailsContent');
            detailsContainer.innerHTML = '';
        }

        // 显示/隐藏打字指示器
        function showTypingIndicator(show) {
            const indicator = document.getElementById('typingIndicator');
            indicator.className = show ? 'typing-indicator show' : 'typing-indicator';
        }

        // 切换发送按钮状态
        function toggleSendButton(enabled) {
            const button = document.getElementById('sendButton');
            button.disabled = !enabled;
            button.textContent = enabled ? '发送' : '处理中...';
        }

        // 回车发送消息
        document.getElementById('messageInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });

        // 页面卸载时关闭SSE连接
        window.addEventListener('beforeunload', function() {
            if (eventSource) {
                eventSource.close();
            }
        });
    </script>
</body>
</html>