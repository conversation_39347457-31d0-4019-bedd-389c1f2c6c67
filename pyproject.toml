[project]
name = "agentic-rag"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.13"
dependencies = [
    "requests>=2.31.0",
    "python-dotenv>=1.0.0",
    "ddgs>=6.1.0",
    "beautifulsoup4>=4.12.0",
    "lxml>=4.9.0",
    "aiohttp>=3.8.0",
    "fastapi>=0.116.1",
    "uvicorn>=0.35.0",
    "websockets>=15.0.1",
    "flask>=3.1.2",
    "flask-cors>=6.0.1",
]
